import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import ProductCard from './ProductCard';
import { ProductType } from '../../types/product.d';
import useApi from '../../hooks/useApi';
import { MAIN_URL } from '../../constant/urls';

interface RecentlyViewedProductsProps {
  limit?: number;
  showTitle?: boolean;
  className?: string;
}

export const RecentlyViewedProducts: React.FC<RecentlyViewedProductsProps> = ({
  limit = 5,
  showTitle = true,
  className = ""
}) => {
  const [recentlyViewed, setRecentlyViewed] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { read } = useApi(MAIN_URL || '');
  const { status } = useSession();

  useEffect(() => {
    const fetchRecentlyViewed = async () => {
      if (status === 'authenticated') {
        setLoading(true);
        try {
          const response = await read('/api/v1/users/recently-viewed/');
          const data = Array.isArray(response) ? response : [];
          setRecentlyViewed(data);
        } catch (error) {
          console.error('Error fetching recently viewed:', error);
          setRecentlyViewed([]);
        } finally {
          setLoading(false);
        }
      } else {
        setRecentlyViewed([]);
        setLoading(false);
      }
    };

    fetchRecentlyViewed();
  }, [status]);

  if (status !== 'authenticated' || (!loading && !recentlyViewed?.length)) {
    return null;
  }

  const displayProducts = recentlyViewed.slice(0, limit);

  // Transform products to match ProductType interface
  const transformedProducts: ProductType[] = displayProducts.map(product => ({
    id: product.id,
    name: product.name,
    price: product.price,
    rating: 0, // Default rating since it's not in our API response
    image: product.images?.[0]?.image || '/placeholder.jpg',
    category: product.category?.name || '',
    brand: product.brand?.name || '',
    slug: product.slug
  }));

  if (loading) {
    return (
      <div className={`recently-viewed-products ${className}`}>
        {showTitle && (
          <h3 className="text-lg font-semibold mb-4">Recently Viewed</h3>
        )}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {Array.from({ length: limit }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-300 h-48 rounded-lg mb-2"></div>
              <div className="bg-gray-300 h-4 rounded mb-1"></div>
              <div className="bg-gray-300 h-4 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`recently-viewed-products ${className}`}>
      {showTitle && (
        <h3 className="text-lg font-semibold mb-4">Recently Viewed</h3>
      )}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {transformedProducts.map((product) => (
          <ProductCard key={product.id} {...product} />
        ))}
      </div>
    </div>
  );
};
