# Recently Viewed Products & Product Recommendations - Detailed Implementation Plan

## Overview
This document outlines the implementation plan for adding a comprehensive "Recently Viewed" feature and intelligent product recommendation system to track, display, and suggest products for users. The implementation will be designed to work seamlessly with the existing codebase without breaking any current functionality.

## New Features Added to Plan
- **Product Recommendations Engine**: Intelligent product suggestions based on user behavior
- **Multiple Recommendation Types**: Category-based, brand-based, price-based, and collaborative filtering
- **Enhanced Related Products**: Improved algorithm beyond simple category matching
- **Personalized Suggestions**: User-specific recommendations based on viewing and purchase history
- **Cross-selling & Upselling**: Strategic product suggestions to increase sales

## Current Architecture Analysis

### Backend (Django)
- **Framework**: Django with Django REST Framework
- **Database**: PostgreSQL with replica support
- **Authentication**: JWT tokens with NextAuth.js integration
- **Caching**: Redis with fallback to LocMemCache
- **Security**: Comprehensive security monitoring with SecurityEvent model
- **User Model**: Custom `Customer` model extending AbstractUser
- **Product Model**: Comprehensive product model with optimized queries

### Frontend (Next.js)
- **Framework**: Next.js 14 with TypeScript
- **Authentication**: NextAuth.js with JWT
- **State Management**: React Context + Custom hooks
- **Storage**: Custom `useStorage` hook for localStorage/sessionStorage
- **API Integration**: Custom `useApi` hook for API calls

## Implementation Strategy

### Phase 1: Backend Implementation

#### 1.1 Database Models
Create new models to track recently viewed products and recommendations:

```python
# users/models.py - Add to existing models
class RecentlyViewedProduct(models.Model):
    user = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name="recently_viewed"
    )
    product = models.ForeignKey(
        "products.Product",
        on_delete=models.CASCADE
    )
    viewed_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("user", "product")
        ordering = ["-viewed_at"]
        indexes = [
            models.Index(fields=['user', '-viewed_at']),
            models.Index(fields=['product', '-viewed_at']),
        ]

    def __str__(self):
        return f"{self.user.email} viewed {self.product.name}"


class ProductRecommendation(models.Model):
    """Store computed product recommendations for users"""
    RECOMMENDATION_TYPES = [
        ('category_based', 'Category Based'),
        ('brand_based', 'Brand Based'),
        ('price_based', 'Price Based'),
        ('collaborative', 'Collaborative Filtering'),
        ('frequently_bought', 'Frequently Bought Together'),
        ('trending', 'Trending Products'),
        ('seasonal', 'Seasonal Recommendations'),
    ]

    user = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name="recommendations"
    )
    product = models.ForeignKey(
        "products.Product",
        on_delete=models.CASCADE
    )
    recommendation_type = models.CharField(
        max_length=20,
        choices=RECOMMENDATION_TYPES
    )
    score = models.FloatField(
        help_text="Recommendation confidence score (0.0 to 1.0)"
    )
    source_product = models.ForeignKey(
        "products.Product",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="generated_recommendations",
        help_text="Product that triggered this recommendation"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("user", "product", "recommendation_type", "source_product")
        ordering = ["-score", "-created_at"]
        indexes = [
            models.Index(fields=['user', 'recommendation_type', '-score']),
            models.Index(fields=['product', 'recommendation_type']),
            models.Index(fields=['source_product', 'recommendation_type']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.recommendation_type} recommendation: {self.product.name} for {self.user.email}"


class UserProductInteraction(models.Model):
    """Track user interactions with products for recommendation engine"""
    INTERACTION_TYPES = [
        ('view', 'Product View'),
        ('add_to_cart', 'Add to Cart'),
        ('remove_from_cart', 'Remove from Cart'),
        ('purchase', 'Purchase'),
        ('wishlist_add', 'Add to Wishlist'),
        ('wishlist_remove', 'Remove from Wishlist'),
        ('review', 'Product Review'),
        ('search', 'Search Result Click'),
    ]

    user = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name="product_interactions"
    )
    product = models.ForeignKey(
        "products.Product",
        on_delete=models.CASCADE,
        related_name="user_interactions"
    )
    interaction_type = models.CharField(
        max_length=20,
        choices=INTERACTION_TYPES
    )
    interaction_weight = models.FloatField(
        default=1.0,
        help_text="Weight of this interaction for recommendations"
    )
    session_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Session ID for anonymous user tracking"
    )
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional interaction data (search query, cart quantity, etc.)"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=['user', 'interaction_type', '-created_at']),
            models.Index(fields=['product', 'interaction_type', '-created_at']),
            models.Index(fields=['session_id', '-created_at']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.user.email} {self.interaction_type} {self.product.name}"
```

#### 1.2 Recommendation Engine Service
Create a comprehensive recommendation service:

```python
# users/recommendation_engine.py - New file
from typing import List, Dict, Tuple, Optional
from django.db.models import Count, Q, Avg, F
from django.contrib.auth import get_user_model
from products.models import Product, Category, Brand
from orders.models import Order, OrderItem
from .models import (
    RecentlyViewedProduct,
    ProductRecommendation,
    UserProductInteraction
)
import logging
from decimal import Decimal
from datetime import datetime, timedelta
from django.utils import timezone

logger = logging.getLogger(__name__)
Customer = get_user_model()


class RecommendationEngine:
    """Advanced product recommendation engine"""

    # Interaction weights for recommendation scoring
    INTERACTION_WEIGHTS = {
        'view': 1.0,
        'add_to_cart': 3.0,
        'purchase': 10.0,
        'wishlist_add': 2.0,
        'review': 5.0,
        'search': 1.5,
    }

    # Time decay factors (how much to reduce score based on age)
    TIME_DECAY_DAYS = {
        'recent': 7,      # Full weight
        'medium': 30,     # 70% weight
        'old': 90,        # 30% weight
        'very_old': 365,  # 10% weight
    }

    def __init__(self, user: Customer):
        self.user = user
        self.user_interactions = UserProductInteraction.objects.filter(user=user)
        self.recently_viewed = RecentlyViewedProduct.objects.filter(user=user)

    def generate_all_recommendations(self, limit_per_type: int = 10) -> Dict[str, List[Product]]:
        """Generate all types of recommendations for the user"""
        recommendations = {}

        try:
            recommendations['category_based'] = self.get_category_based_recommendations(limit_per_type)
            recommendations['brand_based'] = self.get_brand_based_recommendations(limit_per_type)
            recommendations['price_based'] = self.get_price_based_recommendations(limit_per_type)
            recommendations['collaborative'] = self.get_collaborative_recommendations(limit_per_type)
            recommendations['frequently_bought'] = self.get_frequently_bought_together(limit_per_type)
            recommendations['trending'] = self.get_trending_products(limit_per_type)

            # Store recommendations in database for caching
            self._store_recommendations(recommendations)

        except Exception as e:
            logger.error(f"Error generating recommendations for user {self.user.id}: {e}")

        return recommendations

    def get_category_based_recommendations(self, limit: int = 10) -> List[Product]:
        """Recommend products from categories user has shown interest in"""
        # Get user's preferred categories based on interactions
        category_scores = {}

        for interaction in self.user_interactions.select_related('product__category'):
            if interaction.product.category:
                category = interaction.product.category
                weight = self.INTERACTION_WEIGHTS.get(interaction.interaction_type, 1.0)
                time_weight = self._calculate_time_weight(interaction.created_at)

                if category.id not in category_scores:
                    category_scores[category.id] = 0
                category_scores[category.id] += weight * time_weight

        # Get top categories
        top_categories = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)[:5]

        if not top_categories:
            return []

        # Get products from top categories, excluding already interacted products
        interacted_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        recommended_products = []
        for category_id, score in top_categories:
            products = Product.objects.filter(
                category_id=category_id,
                is_active=True
            ).exclude(
                id__in=interacted_product_ids
            ).order_by('-created_at')[:limit//len(top_categories) + 1]

            recommended_products.extend(products)

        return recommended_products[:limit]

    def get_brand_based_recommendations(self, limit: int = 10) -> List[Product]:
        """Recommend products from brands user has shown interest in"""
        # Similar logic to category-based but for brands
        brand_scores = {}

        for interaction in self.user_interactions.select_related('product__brand'):
            if interaction.product.brand:
                brand = interaction.product.brand
                weight = self.INTERACTION_WEIGHTS.get(interaction.interaction_type, 1.0)
                time_weight = self._calculate_time_weight(interaction.created_at)

                if brand.id not in brand_scores:
                    brand_scores[brand.id] = 0
                brand_scores[brand.id] += weight * time_weight

        top_brands = sorted(brand_scores.items(), key=lambda x: x[1], reverse=True)[:3]

        if not top_brands:
            return []

        interacted_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        recommended_products = []
        for brand_id, score in top_brands:
            products = Product.objects.filter(
                brand_id=brand_id,
                is_active=True
            ).exclude(
                id__in=interacted_product_ids
            ).order_by('-created_at')[:limit//len(top_brands) + 1]

            recommended_products.extend(products)

        return recommended_products[:limit]

    def get_price_based_recommendations(self, limit: int = 10) -> List[Product]:
        """Recommend products in user's preferred price range"""
        # Calculate user's price preferences
        user_purchases = self.user_interactions.filter(
            interaction_type='purchase'
        ).select_related('product')

        if not user_purchases.exists():
            # Fallback to viewed products
            user_purchases = self.user_interactions.filter(
                interaction_type='view'
            ).select_related('product')

        if not user_purchases.exists():
            return []

        prices = [interaction.product.price for interaction in user_purchases]
        avg_price = sum(prices) / len(prices)

        # Define price range (±30% of average)
        price_min = avg_price * Decimal('0.7')
        price_max = avg_price * Decimal('1.3')

        interacted_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        return Product.objects.filter(
            price__gte=price_min,
            price__lte=price_max,
            is_active=True
        ).exclude(
            id__in=interacted_product_ids
        ).order_by('?')[:limit]  # Random order for diversity

    def get_collaborative_recommendations(self, limit: int = 10) -> List[Product]:
        """Recommend products based on similar users' behavior"""
        # Find users with similar interaction patterns
        user_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        if len(user_product_ids) < 3:
            return []  # Need minimum interactions for collaborative filtering

        # Find users who interacted with similar products
        similar_users = Customer.objects.filter(
            product_interactions__product_id__in=user_product_ids
        ).exclude(
            id=self.user.id
        ).annotate(
            common_products=Count('product_interactions__product_id')
        ).filter(
            common_products__gte=2  # At least 2 common products
        ).order_by('-common_products')[:20]  # Top 20 similar users

        # Get products these similar users interacted with
        similar_user_products = UserProductInteraction.objects.filter(
            user__in=similar_users,
            interaction_type__in=['purchase', 'add_to_cart', 'wishlist_add']
        ).exclude(
            product_id__in=user_product_ids
        ).values('product_id').annotate(
            interaction_count=Count('id')
        ).order_by('-interaction_count')

        product_ids = [item['product_id'] for item in similar_user_products[:limit]]

        return Product.objects.filter(
            id__in=product_ids,
            is_active=True
        ).order_by('?')[:limit]

    def get_frequently_bought_together(self, limit: int = 10) -> List[Product]:
        """Recommend products frequently bought together with user's purchases"""
        # Get user's purchased products
        user_purchases = self.user_interactions.filter(
            interaction_type='purchase'
        ).values_list('product_id', flat=True)

        if not user_purchases:
            return []

        # Find products frequently bought together
        frequently_together = OrderItem.objects.filter(
            order__items__product_id__in=user_purchases
        ).exclude(
            product_id__in=user_purchases
        ).values('product_id').annotate(
            frequency=Count('order_id', distinct=True)
        ).filter(
            frequency__gte=2  # Bought together at least twice
        ).order_by('-frequency')

        product_ids = [item['product_id'] for item in frequently_together[:limit]]

        return Product.objects.filter(
            id__in=product_ids,
            is_active=True
        )[:limit]

    def get_trending_products(self, limit: int = 10) -> List[Product]:
        """Get currently trending products based on recent interactions"""
        # Products with high interaction volume in last 7 days
        week_ago = timezone.now() - timedelta(days=7)

        trending = UserProductInteraction.objects.filter(
            created_at__gte=week_ago
        ).values('product_id').annotate(
            interaction_score=Count('id') * 1.0 +  # Base interactions
            Count('id', filter=Q(interaction_type='purchase')) * 5.0 +  # Purchases weighted more
            Count('id', filter=Q(interaction_type='add_to_cart')) * 2.0  # Cart adds weighted more
        ).order_by('-interaction_score')

        # Exclude products user already interacted with
        interacted_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        trending_product_ids = [
            item['product_id'] for item in trending
            if item['product_id'] not in interacted_product_ids
        ][:limit]

        return Product.objects.filter(
            id__in=trending_product_ids,
            is_active=True
        )[:limit]

    def _calculate_time_weight(self, interaction_date: datetime) -> float:
        """Calculate time-based weight for interactions"""
        days_ago = (timezone.now() - interaction_date).days

        if days_ago <= self.TIME_DECAY_DAYS['recent']:
            return 1.0
        elif days_ago <= self.TIME_DECAY_DAYS['medium']:
            return 0.7
        elif days_ago <= self.TIME_DECAY_DAYS['old']:
            return 0.3
        elif days_ago <= self.TIME_DECAY_DAYS['very_old']:
            return 0.1
        else:
            return 0.05  # Very old interactions have minimal weight

    def _store_recommendations(self, recommendations: Dict[str, List[Product]]):
        """Store generated recommendations in database for caching"""
        # Clear existing recommendations for this user
        ProductRecommendation.objects.filter(user=self.user).delete()

        recommendation_objects = []
        for rec_type, products in recommendations.items():
            for i, product in enumerate(products):
                score = 1.0 - (i * 0.1)  # Decreasing score based on position
                recommendation_objects.append(
                    ProductRecommendation(
                        user=self.user,
                        product=product,
                        recommendation_type=rec_type,
                        score=max(score, 0.1)  # Minimum score of 0.1
                    )
                )

        if recommendation_objects:
            ProductRecommendation.objects.bulk_create(recommendation_objects)

    @classmethod
    def track_interaction(cls, user: Customer, product: Product,
                         interaction_type: str, session_id: str = None,
                         metadata: dict = None):
        """Track user interaction with a product"""
        UserProductInteraction.objects.create(
            user=user,
            product=product,
            interaction_type=interaction_type,
            interaction_weight=cls.INTERACTION_WEIGHTS.get(interaction_type, 1.0),
            session_id=session_id,
            metadata=metadata or {}
        )
```

#### 1.3 API Endpoints
Add new endpoints to handle recently viewed products and recommendations:

```python
# users/urls.py - Add to existing urlpatterns
path("recently-viewed/", RecentlyViewedListView.as_view(), name="recently-viewed-list"),
path("recently-viewed/add/", AddRecentlyViewedView.as_view(), name="recently-viewed-add"),
path("recently-viewed/clear/", ClearRecentlyViewedView.as_view(), name="recently-viewed-clear"),

# New recommendation endpoints
path("recommendations/", RecommendationListView.as_view(), name="recommendations-list"),
path("recommendations/<str:recommendation_type>/", RecommendationByTypeView.as_view(), name="recommendations-by-type"),
path("recommendations/generate/", GenerateRecommendationsView.as_view(), name="generate-recommendations"),
path("recommendations/for-product/<int:product_id>/", ProductRecommendationsView.as_view(), name="product-recommendations"),
path("interactions/track/", TrackInteractionView.as_view(), name="track-interaction"),
```

#### 1.4 Views Implementation
Create views following existing patterns:

```python
# users/views.py - Add to existing views
from .recommendation_engine import RecommendationEngine
from .models import RecentlyViewedProduct, ProductRecommendation, UserProductInteraction

class RecentlyViewedListView(generics.ListAPIView):
    serializer_class = ProductListSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None  # No pagination for recently viewed

    def get_queryset(self):
        # Get last 10 recently viewed products
        recently_viewed = RecentlyViewedProduct.objects.filter(
            user=self.request.user
        ).select_related('product')[:10]

        product_ids = [rv.product.id for rv in recently_viewed]

        # Use existing optimized product queryset
        from products.views import get_optimized_product_queryset
        products = get_optimized_product_queryset(
            Product.objects.filter(id__in=product_ids, is_active=True)
        )

        # Maintain order from recently_viewed
        product_dict = {p.id: p for p in products}
        return [product_dict[pid] for pid in product_ids if pid in product_dict]


class AddRecentlyViewedView(generics.CreateAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        product_id = request.data.get('product_id')
        if not product_id:
            return Response({'error': 'product_id is required'},
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            product = Product.objects.get(id=product_id, is_active=True)

            # Update or create recently viewed record
            RecentlyViewedProduct.objects.update_or_create(
                user=request.user,
                product=product,
                defaults={'viewed_at': timezone.now()}
            )

            # Track interaction for recommendations
            RecommendationEngine.track_interaction(
                user=request.user,
                product=product,
                interaction_type='view',
                session_id=request.session.session_key
            )

            return Response({'success': True}, status=status.HTTP_201_CREATED)

        except Product.DoesNotExist:
            return Response({'error': 'Product not found'},
                          status=status.HTTP_404_NOT_FOUND)


class RecommendationListView(generics.ListAPIView):
    serializer_class = ProductListSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        recommendation_type = self.request.query_params.get('type', 'all')
        limit = int(self.request.query_params.get('limit', 20))

        if recommendation_type == 'all':
            # Get mixed recommendations from all types
            recommendations = ProductRecommendation.objects.filter(
                user=self.request.user
            ).select_related('product').order_by('-score')[:limit]
        else:
            recommendations = ProductRecommendation.objects.filter(
                user=self.request.user,
                recommendation_type=recommendation_type
            ).select_related('product').order_by('-score')[:limit]

        product_ids = [rec.product.id for rec in recommendations]

        # Use existing optimized product queryset
        from products.views import get_optimized_product_queryset
        products = get_optimized_product_queryset(
            Product.objects.filter(id__in=product_ids, is_active=True)
        )

        # Maintain recommendation order
        product_dict = {p.id: p for p in products}
        return [product_dict[pid] for pid in product_ids if pid in product_dict]


class RecommendationByTypeView(generics.ListAPIView):
    serializer_class = ProductListSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        recommendation_type = self.kwargs['recommendation_type']
        limit = int(self.request.query_params.get('limit', 10))

        recommendations = ProductRecommendation.objects.filter(
            user=self.request.user,
            recommendation_type=recommendation_type
        ).select_related('product').order_by('-score')[:limit]

        product_ids = [rec.product.id for rec in recommendations]

        from products.views import get_optimized_product_queryset
        products = get_optimized_product_queryset(
            Product.objects.filter(id__in=product_ids, is_active=True)
        )

        product_dict = {p.id: p for p in products}
        return [product_dict[pid] for pid in product_ids if pid in product_dict]


class GenerateRecommendationsView(generics.CreateAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        try:
            engine = RecommendationEngine(request.user)
            recommendations = engine.generate_all_recommendations()

            # Count total recommendations generated
            total_count = sum(len(products) for products in recommendations.values())

            return Response({
                'success': True,
                'message': f'Generated {total_count} recommendations',
                'recommendations_by_type': {
                    rec_type: len(products)
                    for rec_type, products in recommendations.items()
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return Response({
                'success': False,
                'error': 'Failed to generate recommendations'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProductRecommendationsView(generics.ListAPIView):
    """Get recommendations for a specific product (related products)"""
    serializer_class = ProductListSerializer
    permission_classes = [permissions.AllowAny]
    pagination_class = None

    def get_queryset(self):
        product_id = self.kwargs['product_id']
        limit = int(self.request.query_params.get('limit', 8))

        try:
            product = Product.objects.get(id=product_id, is_active=True)

            # Get products from same category and brand
            related_products = Product.objects.filter(
                Q(category=product.category) | Q(brand=product.brand),
                is_active=True
            ).exclude(id=product_id)

            # Add price-based filtering (±50% of product price)
            if product.price:
                price_min = product.price * Decimal('0.5')
                price_max = product.price * Decimal('1.5')
                related_products = related_products.filter(
                    price__gte=price_min,
                    price__lte=price_max
                )

            # Order by relevance (same category first, then brand, then random)
            related_products = related_products.annotate(
                relevance_score=Case(
                    When(category=product.category, then=Value(2)),
                    When(brand=product.brand, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField()
                )
            ).order_by('-relevance_score', '?')[:limit]

            from products.views import get_optimized_product_queryset
            return get_optimized_product_queryset(related_products)

        except Product.DoesNotExist:
            return Product.objects.none()


class TrackInteractionView(generics.CreateAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        product_id = request.data.get('product_id')
        interaction_type = request.data.get('interaction_type')
        metadata = request.data.get('metadata', {})

        if not product_id or not interaction_type:
            return Response({
                'error': 'product_id and interaction_type are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            product = Product.objects.get(id=product_id, is_active=True)

            RecommendationEngine.track_interaction(
                user=request.user,
                product=product,
                interaction_type=interaction_type,
                session_id=request.session.session_key,
                metadata=metadata
            )

            return Response({'success': True}, status=status.HTTP_201_CREATED)

        except Product.DoesNotExist:
            return Response({'error': 'Product not found'},
                          status=status.HTTP_404_NOT_FOUND)
```

#### 1.5 Automatic Tracking
Integrate with existing ProductDetailView and other key views:

```python
# products/views.py - Modify existing ProductDetailView
from users.recommendation_engine import RecommendationEngine

class ProductDetailView(generics.RetrieveAPIView):
    # ... existing code ...

    def retrieve(self, request, *args, **kwargs):
        response = super().retrieve(request, *args, **kwargs)

        # Track recently viewed and interactions for authenticated users
        if request.user.is_authenticated:
            product = self.get_object()
            self._track_recently_viewed(request.user, product)
            self._track_product_interaction(request.user, product)

        return response

    def _track_recently_viewed(self, user, product):
        from users.models import RecentlyViewedProduct

        # Update or create recently viewed record
        RecentlyViewedProduct.objects.update_or_create(
            user=user,
            product=product,
            defaults={'viewed_at': timezone.now()}
        )

        # Keep only last 50 records per user to prevent unlimited growth
        user_recent = RecentlyViewedProduct.objects.filter(user=user)
        if user_recent.count() > 50:
            oldest_records = user_recent[50:]
            RecentlyViewedProduct.objects.filter(
                id__in=[r.id for r in oldest_records]
            ).delete()

    def _track_product_interaction(self, user, product):
        """Track product view for recommendation engine"""
        try:
            RecommendationEngine.track_interaction(
                user=user,
                product=product,
                interaction_type='view',
                session_id=self.request.session.session_key,
                metadata={
                    'source': 'product_detail',
                    'user_agent': self.request.META.get('HTTP_USER_AGENT', ''),
                    'referrer': self.request.META.get('HTTP_REFERER', '')
                }
            )
        except Exception as e:
            # Don't break the main flow if tracking fails
            logger.error(f"Failed to track product interaction: {e}")


# orders/views.py - Enhance existing cart and order views
class AddToCartView(generics.CreateAPIView):
    # ... existing code ...

    def perform_create(self, serializer):
        cart_item = serializer.save()

        # Track add to cart interaction
        if self.request.user.is_authenticated:
            try:
                RecommendationEngine.track_interaction(
                    user=self.request.user,
                    product=cart_item.product,
                    interaction_type='add_to_cart',
                    session_id=self.request.session.session_key,
                    metadata={'quantity': cart_item.quantity}
                )
            except Exception as e:
                logger.error(f"Failed to track add to cart: {e}")


class OrderCreateView(generics.CreateAPIView):
    # ... existing code ...

    def perform_create(self, serializer):
        order = serializer.save()

        # Track purchase interactions for all order items
        if self.request.user.is_authenticated:
            for order_item in order.items.all():
                try:
                    RecommendationEngine.track_interaction(
                        user=self.request.user,
                        product=order_item.product,
                        interaction_type='purchase',
                        session_id=self.request.session.session_key,
                        metadata={
                            'quantity': order_item.quantity,
                            'price': str(order_item.price),
                            'order_id': order.id
                        }
                    )
                except Exception as e:
                    logger.error(f"Failed to track purchase: {e}")


# users/views.py - Enhance wishlist views
class WishlistAddView(generics.CreateAPIView):
    # ... existing code ...

    def perform_create(self, serializer):
        wishlist_item = serializer.save()

        # Track wishlist add interaction
        try:
            RecommendationEngine.track_interaction(
                user=self.request.user,
                product=wishlist_item.product,
                interaction_type='wishlist_add',
                session_id=self.request.session.session_key
            )
        except Exception as e:
            logger.error(f"Failed to track wishlist add: {e}")
```

### Phase 2: Frontend Implementation

#### 2.1 API Integration
Create hooks for recently viewed and recommendations functionality:

```typescript
// hooks/useRecentlyViewed.ts
export const useRecentlyViewed = () => {
  const { data, loading, read } = useApi(MAIN_URL);
  const { create } = useApi(MAIN_URL);
  const { status } = useSession();

  const getRecentlyViewed = useCallback(async () => {
    if (status === 'authenticated') {
      return await read('/api/v1/users/recently-viewed/');
    }
    return [];
  }, [status, read]);

  const addToRecentlyViewed = useCallback(async (productId: number) => {
    if (status === 'authenticated') {
      return await create('/api/v1/users/recently-viewed/add/', {
        product_id: productId
      });
    }
  }, [status, create]);

  return {
    recentlyViewed: data,
    loading,
    getRecentlyViewed,
    addToRecentlyViewed
  };
};

// hooks/useRecommendations.ts
export const useRecommendations = () => {
  const { data, loading, read } = useApi(MAIN_URL);
  const { create } = useApi(MAIN_URL);
  const { status } = useSession();

  const getRecommendations = useCallback(async (type?: string, limit?: number) => {
    if (status === 'authenticated') {
      const params = new URLSearchParams();
      if (type) params.append('type', type);
      if (limit) params.append('limit', limit.toString());

      const url = `/api/v1/users/recommendations/${params.toString() ? '?' + params.toString() : ''}`;
      return await read(url);
    }
    return [];
  }, [status, read]);

  const getRecommendationsByType = useCallback(async (type: string, limit?: number) => {
    if (status === 'authenticated') {
      const params = limit ? `?limit=${limit}` : '';
      return await read(`/api/v1/users/recommendations/${type}/${params}`);
    }
    return [];
  }, [status, read]);

  const generateRecommendations = useCallback(async () => {
    if (status === 'authenticated') {
      return await create('/api/v1/users/recommendations/generate/', {});
    }
  }, [status, create]);

  const getProductRecommendations = useCallback(async (productId: number, limit?: number) => {
    const params = limit ? `?limit=${limit}` : '';
    return await read(`/api/v1/users/recommendations/for-product/${productId}/${params}`);
  }, [read]);

  const trackInteraction = useCallback(async (
    productId: number,
    interactionType: string,
    metadata?: any
  ) => {
    if (status === 'authenticated') {
      return await create('/api/v1/users/interactions/track/', {
        product_id: productId,
        interaction_type: interactionType,
        metadata: metadata || {}
      });
    }
  }, [status, create]);

  return {
    recommendations: data,
    loading,
    getRecommendations,
    getRecommendationsByType,
    generateRecommendations,
    getProductRecommendations,
    trackInteraction
  };
};

// hooks/useProductInteractions.ts
export const useProductInteractions = () => {
  const { trackInteraction } = useRecommendations();
  const { addToRecentlyViewed } = useRecentlyViewed();

  const trackProductView = useCallback(async (productId: number) => {
    await Promise.all([
      addToRecentlyViewed(productId),
      trackInteraction(productId, 'view', { source: 'product_detail' })
    ]);
  }, [addToRecentlyViewed, trackInteraction]);

  const trackAddToCart = useCallback(async (productId: number, quantity: number) => {
    await trackInteraction(productId, 'add_to_cart', { quantity });
  }, [trackInteraction]);

  const trackPurchase = useCallback(async (productId: number, quantity: number, price: number) => {
    await trackInteraction(productId, 'purchase', { quantity, price });
  }, [trackInteraction]);

  const trackWishlistAdd = useCallback(async (productId: number) => {
    await trackInteraction(productId, 'wishlist_add');
  }, [trackInteraction]);

  const trackSearchClick = useCallback(async (productId: number, searchQuery: string) => {
    await trackInteraction(productId, 'search', { search_query: searchQuery });
  }, [trackInteraction]);

  return {
    trackProductView,
    trackAddToCart,
    trackPurchase,
    trackWishlistAdd,
    trackSearchClick
  };
};
```

#### 2.2 Component Implementation
Create reusable components for recently viewed and recommendations:

```typescript
// components/product/RecentlyViewedProducts.tsx
interface RecentlyViewedProductsProps {
  limit?: number;
  showTitle?: boolean;
  className?: string;
}

export const RecentlyViewedProducts: React.FC<RecentlyViewedProductsProps> = ({
  limit = 5,
  showTitle = true,
  className = ""
}) => {
  const { recentlyViewed, loading, getRecentlyViewed } = useRecentlyViewed();
  const { status } = useSession();

  useEffect(() => {
    if (status === 'authenticated') {
      getRecentlyViewed();
    }
  }, [status, getRecentlyViewed]);

  if (status !== 'authenticated' || !recentlyViewed?.length) {
    return null;
  }

  const displayProducts = recentlyViewed.slice(0, limit);

  return (
    <div className={`recently-viewed-products ${className}`}>
      {showTitle && (
        <h3 className="text-lg font-semibold mb-4">Recently Viewed</h3>
      )}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {displayProducts.map((product) => (
          <ProductCard key={product.id} {...product} />
        ))}
      </div>
    </div>
  );
};

// components/product/ProductRecommendations.tsx
interface ProductRecommendationsProps {
  type?: 'category_based' | 'brand_based' | 'price_based' | 'collaborative' | 'trending' | 'all';
  limit?: number;
  title?: string;
  showTitle?: boolean;
  className?: string;
  layout?: 'grid' | 'carousel' | 'list';
}

export const ProductRecommendations: React.FC<ProductRecommendationsProps> = ({
  type = 'all',
  limit = 8,
  title,
  showTitle = true,
  className = "",
  layout = 'grid'
}) => {
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { getRecommendations, getRecommendationsByType } = useRecommendations();
  const { status } = useSession();

  useEffect(() => {
    const fetchRecommendations = async () => {
      if (status === 'authenticated') {
        setLoading(true);
        try {
          const data = type === 'all'
            ? await getRecommendations(undefined, limit)
            : await getRecommendationsByType(type, limit);
          setRecommendations(data || []);
        } catch (error) {
          console.error('Error fetching recommendations:', error);
          setRecommendations([]);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [status, type, limit, getRecommendations, getRecommendationsByType]);

  if (status !== 'authenticated' || (!loading && !recommendations.length)) {
    return null;
  }

  const getTitle = () => {
    if (title) return title;

    const titles = {
      category_based: 'Recommended for You',
      brand_based: 'From Your Favorite Brands',
      price_based: 'In Your Price Range',
      collaborative: 'Customers Also Liked',
      trending: 'Trending Now',
      frequently_bought: 'Frequently Bought Together',
      all: 'Recommended Products'
    };

    return titles[type] || 'Recommended Products';
  };

  if (loading) {
    return (
      <div className={`product-recommendations ${className}`}>
        {showTitle && <h3 className="text-lg font-semibold mb-4">{getTitle()}</h3>}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array.from({ length: limit }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-300 h-48 rounded-lg mb-2"></div>
              <div className="bg-gray-300 h-4 rounded mb-1"></div>
              <div className="bg-gray-300 h-4 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const renderGrid = () => (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {recommendations.map((product) => (
        <ProductCard key={product.id} {...product} />
      ))}
    </div>
  );

  const renderCarousel = () => (
    <div className="overflow-x-auto">
      <div className="flex space-x-4 pb-4">
        {recommendations.map((product) => (
          <div key={product.id} className="flex-shrink-0 w-64">
            <ProductCard {...product} />
          </div>
        ))}
      </div>
    </div>
  );

  const renderList = () => (
    <div className="space-y-4">
      {recommendations.map((product) => (
        <div key={product.id} className="flex space-x-4 p-4 border rounded-lg">
          <img
            src={product.images?.[0]?.image || '/placeholder.jpg'}
            alt={product.name}
            className="w-20 h-20 object-cover rounded"
          />
          <div className="flex-1">
            <h4 className="font-semibold">{product.name}</h4>
            <p className="text-gray-600">{product.brand?.name}</p>
            <p className="text-lg font-bold">₹{product.price}</p>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className={`product-recommendations ${className}`}>
      {showTitle && (
        <h3 className="text-lg font-semibold mb-4">{getTitle()}</h3>
      )}
      {layout === 'grid' && renderGrid()}
      {layout === 'carousel' && renderCarousel()}
      {layout === 'list' && renderList()}
    </div>
  );
};

// components/product/SmartRelatedProducts.tsx - Enhanced version of existing RelatedProducts
interface SmartRelatedProductsProps {
  productId: number;
  categorySlug?: string;
  limit?: number;
  className?: string;
}

export const SmartRelatedProducts: React.FC<SmartRelatedProductsProps> = ({
  productId,
  categorySlug,
  limit = 8,
  className = ""
}) => {
  const [relatedProducts, setRelatedProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { getProductRecommendations } = useRecommendations();

  useEffect(() => {
    const fetchRelatedProducts = async () => {
      setLoading(true);
      try {
        // Try to get smart recommendations first
        const smartRecommendations = await getProductRecommendations(productId, limit);

        if (smartRecommendations && smartRecommendations.length > 0) {
          setRelatedProducts(smartRecommendations);
        } else if (categorySlug) {
          // Fallback to category-based products
          const endpoint = CATEGORIZE_PRODUCTS(categorySlug);
          const response = await axios.get(`${MAIN_URL}${endpoint}`);
          const categoryProducts = response.data?.results?.products || [];

          // Filter out current product
          const filtered = categoryProducts.filter((p: any) => p.id !== productId);
          setRelatedProducts(filtered.slice(0, limit));
        }
      } catch (error) {
        console.error('Error fetching related products:', error);
        setRelatedProducts([]);
      } finally {
        setLoading(false);
      }
    };

    if (productId) {
      fetchRelatedProducts();
    }
  }, [productId, categorySlug, limit, getProductRecommendations]);

  if (loading) {
    return (
      <div className={`mt-16 ${className}`}>
        <h2 className="text-2xl font-bold mb-6">Related Products</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-300 h-48 rounded-lg mb-2"></div>
              <div className="bg-gray-300 h-4 rounded mb-1"></div>
              <div className="bg-gray-300 h-4 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!relatedProducts.length) {
    return null;
  }

  return (
    <div className={`mt-16 ${className}`}>
      <h2 className="text-2xl font-bold mb-6">Related Products</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {relatedProducts.map((product) => (
          <ProductCard key={product.id} {...product} />
        ))}
      </div>
    </div>
  );
};
```

#### 2.3 Integration Points
Add recently viewed and recommendations to key pages:

1. **Product Detail Page**: Automatic tracking + Smart related products
2. **Home Page**: Recently viewed + Mixed recommendations
3. **Account Page**: Full recently viewed list + Personalized recommendations
4. **Shop Page**: Recently viewed sidebar + Category-based recommendations
5. **Search Results**: Track search interactions + Show trending products
6. **Cart Page**: Frequently bought together recommendations
7. **Checkout Success**: Cross-sell recommendations

```typescript
// app/page.tsx - Enhanced Home page integration
export default function HomePage() {
  return (
    <MainHOF>
      {/* Existing home content */}
      <FeaturedProducts />
      <CategoryShowcase />

      {/* Recently viewed section */}
      <RecentlyViewedProducts
        limit={6}
        className="my-8"
      />

      {/* Mixed recommendations */}
      <ProductRecommendations
        type="all"
        limit={8}
        title="Recommended for You"
        className="my-8"
      />

      {/* Trending products */}
      <ProductRecommendations
        type="trending"
        limit={6}
        layout="carousel"
        className="my-8"
      />

      {/* Rest of home content */}
    </MainHOF>
  );
}

// app/product/[slug]/page.tsx - Enhanced Product detail page
export default function ProductDetail() {
  const { trackProductView } = useProductInteractions();

  useEffect(() => {
    if (product?.id) {
      trackProductView(product.id);
    }
  }, [product?.id, trackProductView]);

  return (
    <MainHOF>
      {/* Existing product detail content */}

      {/* Enhanced related products with smart recommendations */}
      <SmartRelatedProducts
        productId={product?.id || 0}
        categorySlug={product?.category?.slug || ""}
        limit={8}
      />

      {/* Frequently bought together */}
      <ProductRecommendations
        type="frequently_bought"
        limit={4}
        title="Frequently Bought Together"
        layout="grid"
        className="mt-8"
      />
    </MainHOF>
  );
}

// app/account/page.tsx - Account page with recommendations
export default function AccountPage() {
  return (
    <MainHOF>
      {/* Existing account content */}

      {/* Recently viewed section */}
      <RecentlyViewedProducts
        limit={10}
        showTitle={true}
        className="mb-8"
      />

      {/* Personalized recommendations */}
      <ProductRecommendations
        type="category_based"
        limit={8}
        title="Based on Your Interests"
        className="mb-8"
      />

      <ProductRecommendations
        type="brand_based"
        limit={6}
        title="From Your Favorite Brands"
        className="mb-8"
      />
    </MainHOF>
  );
}
```

### Phase 3: Advanced Features & Performance Considerations

#### 3.1 Rule-Based Recommendation Algorithms (No ML Required)
The core recommendation system uses traditional algorithmic approaches that are highly effective:

**✅ Already Implemented in Core Plan (No ML):**
1. **Category-Based Recommendations**: Products from user's preferred categories
2. **Brand-Based Recommendations**: Products from user's preferred brands
3. **Price-Based Recommendations**: Products in user's price range
4. **Collaborative Filtering**: Simple user similarity without ML
5. **Frequently Bought Together**: Order co-occurrence analysis
6. **Trending Products**: Recent interaction volume analysis

**✅ Enhanced Rule-Based Algorithms:**
```python
# users/advanced_rules.py - Enhanced rule-based recommendations (No ML)
from django.db.models import Count, Q, Avg, F, Case, When, Value
from decimal import Decimal

class AdvancedRuleEngine:
    """Advanced rule-based recommendations without ML dependencies"""

    def get_smart_similar_products(self, product: Product, limit: int = 10) -> List[Product]:
        """Get similar products using multiple rule-based criteria"""
        similar_products = Product.objects.filter(is_active=True).exclude(id=product.id)

        # Multi-criteria scoring without ML
        similar_products = similar_products.annotate(
            similarity_score=Case(
                # Same category and brand = highest score
                When(Q(category=product.category) & Q(brand=product.brand), then=Value(100)),
                # Same category, different brand = high score
                When(category=product.category, then=Value(80)),
                # Same brand, different category = medium score
                When(brand=product.brand, then=Value(60)),
                # Similar price range (±30%) = medium score
                When(
                    Q(price__gte=product.price * Decimal('0.7')) &
                    Q(price__lte=product.price * Decimal('1.3')),
                    then=Value(40)
                ),
                default=Value(0)
            )
        ).filter(similarity_score__gt=0).order_by('-similarity_score', '?')[:limit]

        return similar_products

    def get_seasonal_recommendations(self, user: Customer, limit: int = 10) -> List[Product]:
        """Get seasonal recommendations based on current date"""
        from datetime import datetime

        current_month = datetime.now().month

        # Define seasonal categories (customize based on your products)
        seasonal_mapping = {
            # Winter months (Dec, Jan, Feb)
            12: ['winter-clothing', 'heaters', 'blankets'],
            1: ['winter-clothing', 'heaters', 'blankets'],
            2: ['winter-clothing', 'heaters', 'blankets'],
            # Summer months (Jun, Jul, Aug)
            6: ['summer-clothing', 'fans', 'coolers'],
            7: ['summer-clothing', 'fans', 'coolers'],
            8: ['summer-clothing', 'fans', 'coolers'],
            # Add more seasonal mappings
        }

        seasonal_categories = seasonal_mapping.get(current_month, [])

        if seasonal_categories:
            return Product.objects.filter(
                category__slug__in=seasonal_categories,
                is_active=True
            ).order_by('?')[:limit]

        return Product.objects.none()

    def get_complementary_products(self, product: Product, limit: int = 8) -> List[Product]:
        """Get complementary products based on predefined rules"""

        # Define complementary product rules (customize for your catalog)
        complementary_rules = {
            'smartphones': ['phone-cases', 'chargers', 'headphones'],
            'laptops': ['laptop-bags', 'mouse', 'keyboards'],
            'cameras': ['memory-cards', 'camera-bags', 'tripods'],
            'clothing': ['shoes', 'accessories', 'bags'],
            # Add more rules based on your product categories
        }

        category_slug = product.category.slug if product.category else ''
        complementary_categories = complementary_rules.get(category_slug, [])

        if complementary_categories:
            return Product.objects.filter(
                category__slug__in=complementary_categories,
                is_active=True
            ).exclude(id=product.id).order_by('?')[:limit]

        return Product.objects.none()

    def get_price_tier_recommendations(self, user: Customer, tier: str, limit: int = 10) -> List[Product]:
        """Get recommendations based on price tiers (budget, mid-range, premium)"""

        # Calculate user's average purchase price
        user_purchases = UserProductInteraction.objects.filter(
            user=user,
            interaction_type='purchase'
        ).select_related('product')

        if user_purchases.exists():
            avg_price = sum(p.product.price for p in user_purchases) / user_purchases.count()
        else:
            avg_price = Decimal('1000')  # Default middle price

        # Define price tiers
        if tier == 'budget':
            max_price = avg_price * Decimal('0.7')
            return Product.objects.filter(
                price__lte=max_price,
                is_active=True
            ).order_by('?')[:limit]
        elif tier == 'premium':
            min_price = avg_price * Decimal('1.5')
            return Product.objects.filter(
                price__gte=min_price,
                is_active=True
            ).order_by('?')[:limit]
        else:  # mid-range
            min_price = avg_price * Decimal('0.8')
            max_price = avg_price * Decimal('1.2')
            return Product.objects.filter(
                price__gte=min_price,
                price__lte=max_price,
                is_active=True
            ).order_by('?')[:limit]

    def get_new_arrivals_for_user(self, user: Customer, limit: int = 10) -> List[Product]:
        """Get new arrivals in user's preferred categories"""
        from datetime import timedelta
        from django.utils import timezone

        # Get user's preferred categories
        user_categories = UserProductInteraction.objects.filter(
            user=user
        ).values_list('product__category', flat=True).distinct()

        # Get products added in last 30 days from preferred categories
        thirty_days_ago = timezone.now() - timedelta(days=30)

        return Product.objects.filter(
            category__in=user_categories,
            created_at__gte=thirty_days_ago,
            is_active=True
        ).order_by('-created_at')[:limit]
```

#### 3.2 Privacy Compliance
- Integrate with existing consent management
- Add recently viewed and recommendations to data export functionality
- Include in data deletion requests
- Respect user privacy preferences
- Anonymous user tracking with session-based storage
- GDPR-compliant data retention policies

#### 3.3 Performance Optimization
- Use existing Redis caching patterns for recommendations
- Implement database indexes for efficient queries
- Limit storage to prevent unlimited growth
- Use optimized product querysets
- Background task processing for recommendation generation
- Caching of computed recommendations

#### 3.4 Background Task Processing
```python
# users/tasks.py - Celery tasks for recommendation processing
from celery import shared_task
from django.contrib.auth import get_user_model
from .recommendation_engine import RecommendationEngine
import logging

logger = logging.getLogger(__name__)
Customer = get_user_model()

@shared_task
def generate_user_recommendations(user_id: int):
    """Background task to generate recommendations for a user"""
    try:
        user = Customer.objects.get(id=user_id)
        engine = RecommendationEngine(user)
        recommendations = engine.generate_all_recommendations()

        logger.info(f"Generated recommendations for user {user_id}")
        return {
            'success': True,
            'user_id': user_id,
            'recommendations_count': sum(len(products) for products in recommendations.values())
        }
    except Exception as e:
        logger.error(f"Failed to generate recommendations for user {user_id}: {e}")
        return {'success': False, 'error': str(e)}

@shared_task
def batch_generate_recommendations():
    """Generate recommendations for all active users"""
    active_users = Customer.objects.filter(
        is_active=True,
        product_interactions__isnull=False
    ).distinct()

    for user in active_users:
        generate_user_recommendations.delay(user.id)

    return f"Queued recommendation generation for {active_users.count()} users"

@shared_task
def cleanup_old_interactions():
    """Clean up old interaction data to maintain performance"""
    from datetime import timedelta
    from django.utils import timezone

    # Remove interactions older than 2 years
    cutoff_date = timezone.now() - timedelta(days=730)

    deleted_count = UserProductInteraction.objects.filter(
        created_at__lt=cutoff_date
    ).delete()[0]

    logger.info(f"Cleaned up {deleted_count} old interactions")
    return deleted_count
```

#### 3.5 Fallback for Anonymous Users
Implement localStorage fallback for non-authenticated users:

```typescript
// lib/recentlyViewedManager.ts
class RecentlyViewedManager {
  private storageKey = 'recently_viewed_products';
  private maxItems = 10;
  
  addProduct(product: ProductType) {
    const storage = useStorage('local');
    const existing = this.getProducts();
    
    // Remove if already exists
    const filtered = existing.filter(p => p.id !== product.id);
    
    // Add to beginning
    const updated = [product, ...filtered].slice(0, this.maxItems);
    
    storage.setItem(this.storageKey, JSON.stringify(updated));
  }
  
  getProducts(): ProductType[] {
    const storage = useStorage('local');
    const stored = storage.getItem(this.storageKey);
    return stored ? JSON.parse(stored) : [];
  }
  
  clearProducts() {
    const storage = useStorage('local');
    storage.removeItem(this.storageKey);
  }
}
```

## Implementation Timeline

### Week 1: Backend Foundation
- [ ] Create RecentlyViewedProduct, ProductRecommendation, and UserProductInteraction models
- [ ] Run migrations
- [ ] Implement RecommendationEngine service
- [ ] Create basic API endpoints for recently viewed

### Week 2: Recommendation System Backend
- [ ] Implement all recommendation algorithms (category, brand, price, collaborative)
- [ ] Create recommendation API endpoints
- [ ] Add interaction tracking to existing views
- [ ] Implement background task processing

### Week 3: Frontend Hooks & Components
- [ ] Create useRecentlyViewed and useRecommendations hooks
- [ ] Implement RecentlyViewedProducts component
- [ ] Create ProductRecommendations component
- [ ] Add localStorage fallback for anonymous users

### Week 4: Frontend Integration
- [ ] Enhance product detail pages with smart recommendations
- [ ] Add recommendations to home page
- [ ] Integrate with account page
- [ ] Implement mobile-responsive design

### Week 5: Advanced Features
- [ ] Add frequently bought together recommendations
- [ ] Implement trending products algorithm
- [ ] Create recommendation analytics dashboard
- [ ] Add A/B testing framework for recommendations

### Week 6: Testing & Optimization
- [ ] Write comprehensive tests for all recommendation types
- [ ] Performance optimization and caching
- [ ] Privacy compliance verification
- [ ] Documentation and deployment

### Week 7: Advanced Rule-Based Features (No ML Required)
- [ ] Implement seasonal recommendations
- [ ] Add complementary product suggestions
- [ ] Create price tier recommendations
- [ ] Deploy advanced rule-based algorithms

### Future Phase: Machine Learning Enhancement (Optional)
- [ ] Implement content-based filtering with TF-IDF
- [ ] Add collaborative filtering with matrix factorization
- [ ] Create ML model training pipeline
- [ ] Deploy ML-enhanced recommendations

## Testing Strategy

### Backend Tests
- Model validation and constraints for all new models
- API endpoint functionality for recently viewed and recommendations
- Recommendation algorithm accuracy and performance
- Automatic tracking behavior across all interaction types
- Performance with large datasets and concurrent users
- Background task processing and error handling

### Frontend Tests
- Component rendering for all recommendation types
- Hook functionality and error handling
- localStorage fallback for anonymous users
- User interaction flows and tracking
- Responsive design across devices
- Loading states and error boundaries

### Integration Tests
- End-to-end recommendation flow
- Cross-browser compatibility
- Performance testing with large product catalogs
- A/B testing framework validation

### Recommendation Quality Tests
- Precision and recall metrics
- Diversity of recommendations
- Cold start problem handling
- Real-time recommendation updates

## Security Considerations

1. **Data Privacy**: Integrate with existing privacy framework
2. **Authentication**: Respect existing JWT authentication
3. **Rate Limiting**: Use existing throttling patterns
4. **Data Retention**: Automatic cleanup of old records

## Deployment Strategy

1. **Database Migration**: Safe migration with zero downtime
2. **Feature Flags**: Gradual rollout capability
3. **Monitoring**: Integration with existing security monitoring
4. **Rollback Plan**: Easy rollback if issues arise

## Success Metrics

### User Engagement Metrics
- Click-through rates from recently viewed sections
- Click-through rates from recommendation sections by type
- Time spent on recommended products
- Conversion rates from recommendations to purchases
- User session duration increase
- Return user engagement with recommendations

### Business Metrics
- Revenue increase from recommendation-driven purchases
- Average order value impact from cross-sell recommendations
- Cart abandonment reduction through relevant suggestions
- Customer lifetime value improvement
- Product discovery rate increase

### Technical Metrics
- API response times for recommendation endpoints
- Database query performance for recommendation algorithms
- Cache hit rates for recommendation data
- Background task processing efficiency
- System resource utilization

### Quality Metrics
- Recommendation relevance scores
- Diversity of recommended products
- Coverage of product catalog in recommendations
- User feedback on recommendation quality
- A/B testing results for different recommendation strategies

## Advanced Features for Future Phases

### Phase 4: Advanced Rule-Based Intelligence (No ML)
- Advanced multi-criteria product similarity scoring
- Seasonal and event-based recommendation rules
- Complementary product suggestion engine
- Price tier and budget-aware recommendations
- Time-based recommendation adjustments
- Geographic and demographic rule-based targeting

### Phase 5: AI-Powered Recommendations (Optional ML Enhancement)
- Deep learning models for personalized recommendations
- Natural language processing for product description analysis
- Computer vision for image-based product similarity
- Real-time recommendation updates based on current session behavior

### Phase 6: Social Recommendations
- Friend-based recommendations
- Social proof integration
- Trending products in user's network
- Collaborative wishlists and recommendations

### Phase 7: Contextual Recommendations
- Location-based recommendations
- Time-sensitive recommendations (seasonal, events)
- Weather-based product suggestions
- Device-specific recommendations (mobile vs desktop)

This comprehensive implementation plan ensures the recently viewed feature and intelligent recommendation system integrate seamlessly with the existing architecture while providing a foundation for advanced AI-powered personalization features.

## Detailed Technical Implementation

### Database Schema Details

```sql
-- Migration file: users/migrations/XXXX_add_recently_viewed.py
CREATE TABLE users_recentlyviewedproduct (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users_customer(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products_product(id) ON DELETE CASCADE,
    viewed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

CREATE INDEX idx_recently_viewed_user_time ON users_recentlyviewedproduct(user_id, viewed_at DESC);
CREATE INDEX idx_recently_viewed_product_time ON users_recentlyviewedproduct(product_id, viewed_at DESC);
```

### API Endpoint Specifications

#### GET /api/v1/users/recently-viewed/
**Response Format:**
```json
{
  "count": 5,
  "results": [
    {
      "id": 123,
      "name": "Product Name",
      "slug": "product-slug",
      "price": "299.99",
      "images": [{"image": "url"}],
      "category": {"name": "Category", "slug": "category-slug"},
      "brand": {"name": "Brand"},
      "average_rating": 4.5,
      "viewed_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### POST /api/v1/users/recently-viewed/add/
**Request Body:**
```json
{
  "product_id": 123
}
```

**Response:**
```json
{
  "success": true,
  "message": "Product added to recently viewed"
}
```

### Frontend Component Architecture

#### RecentlyViewedProducts Component Props
```typescript
interface RecentlyViewedProductsProps {
  limit?: number;           // Default: 5
  showTitle?: boolean;      // Default: true
  layout?: 'grid' | 'carousel' | 'list'; // Default: 'grid'
  className?: string;
  onProductClick?: (product: ProductType) => void;
  showClearButton?: boolean; // Default: false
}
```

#### Integration with Existing Components
```typescript
// app/page.tsx - Home page integration
export default function HomePage() {
  return (
    <MainHOF>
      {/* Existing home content */}
      <FeaturedProducts />
      <CategoryShowcase />

      {/* New recently viewed section */}
      <RecentlyViewedProducts
        limit={6}
        layout="carousel"
        className="my-8"
      />

      {/* Rest of home content */}
    </MainHOF>
  );
}
```

### Performance Optimization Strategies

#### 1. Database Query Optimization
```python
# Optimized query with prefetch_related
def get_recently_viewed_products(user, limit=10):
    recently_viewed = RecentlyViewedProduct.objects.filter(
        user=user
    ).select_related('product__category', 'product__brand').prefetch_related(
        'product__images'
    ).order_by('-viewed_at')[:limit]

    return [rv.product for rv in recently_viewed if rv.product.is_active]
```

#### 2. Redis Caching Strategy
```python
# Cache recently viewed products for 1 hour
def get_cached_recently_viewed(user_id, limit=10):
    cache_key = f"recently_viewed:{user_id}:{limit}"
    cached_data = cache.get(cache_key)

    if cached_data is None:
        products = get_recently_viewed_products(user_id, limit)
        cache.set(cache_key, products, timeout=3600)  # 1 hour
        return products

    return cached_data
```

#### 3. Frontend Caching
```typescript
// Use React Query for client-side caching
const useRecentlyViewedQuery = () => {
  const { status } = useSession();

  return useQuery({
    queryKey: ['recently-viewed'],
    queryFn: fetchRecentlyViewed,
    enabled: status === 'authenticated',
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};
```

### Privacy and GDPR Compliance

#### 1. Consent Integration
```python
# Check user consent before tracking
def track_product_view(user, product):
    if not user.has_analytics_consent():
        return False

    RecentlyViewedProduct.objects.update_or_create(
        user=user,
        product=product,
        defaults={'viewed_at': timezone.now()}
    )
    return True
```

#### 2. Data Export Integration
```python
# Add to existing data export functionality
def export_user_data(user):
    data = {
        # ... existing export data
        'recently_viewed': [
            {
                'product_name': rv.product.name,
                'product_slug': rv.product.slug,
                'viewed_at': rv.viewed_at.isoformat(),
            }
            for rv in user.recently_viewed.all()
        ]
    }
    return data
```

#### 3. Data Deletion Integration
```python
# Add to existing data deletion process
def delete_user_data(user):
    # ... existing deletion logic

    # Delete recently viewed products
    RecentlyViewedProduct.objects.filter(user=user).delete()
```

### Mobile Responsiveness

#### Responsive Grid Layout
```css
/* styles/recently-viewed.css */
.recently-viewed-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 768px) {
  .recently-viewed-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .recently-viewed-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}
```

#### Mobile Carousel Implementation
```typescript
// Use existing Swiper integration
const RecentlyViewedCarousel = ({ products }: { products: ProductType[] }) => {
  return (
    <Swiper
      spaceBetween={16}
      slidesPerView={2}
      breakpoints={{
        640: { slidesPerView: 3 },
        768: { slidesPerView: 4 },
        1024: { slidesPerView: 5 },
      }}
    >
      {products.map((product) => (
        <SwiperSlide key={product.id}>
          <ProductCard {...product} />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};
```

### Error Handling and Fallbacks

#### Backend Error Handling
```python
def track_recently_viewed_safe(user, product):
    try:
        track_product_view(user, product)
    except Exception as e:
        # Log error but don't break the main flow
        logger.error(f"Failed to track recently viewed: {e}")
        # Optionally send to error monitoring service
```

#### Frontend Error Boundaries
```typescript
const RecentlyViewedWithErrorBoundary = () => {
  return (
    <ErrorBoundary
      fallback={<div>Unable to load recently viewed products</div>}
      onError={(error) => console.error('Recently viewed error:', error)}
    >
      <RecentlyViewedProducts />
    </ErrorBoundary>
  );
};
```

### Analytics and Monitoring

#### Track User Engagement
```python
# Add analytics events
def track_recently_viewed_click(user, product, source='recently_viewed'):
    SecurityEvent.objects.create(
        user=user,
        event_type='PRODUCT_INTERACTION',
        ip_address=get_client_ip(request),
        details={
            'action': 'recently_viewed_click',
            'product_id': product.id,
            'product_name': product.name,
            'source': source,
        }
    )
```

#### Performance Monitoring
```python
# Monitor query performance
def monitor_recently_viewed_performance():
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT
                COUNT(*) as total_records,
                AVG(EXTRACT(EPOCH FROM (NOW() - viewed_at))) as avg_age_seconds
            FROM users_recentlyviewedproduct
            WHERE viewed_at > NOW() - INTERVAL '30 days'
        """)
        return cursor.fetchone()
```

This comprehensive implementation plan provides all the technical details needed to implement the recently viewed feature while maintaining the existing codebase integrity and following established patterns.
