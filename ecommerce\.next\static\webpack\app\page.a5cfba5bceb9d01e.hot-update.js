"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/useRecommendations.ts":
/*!*************************************!*\
  !*** ./hooks/useRecommendations.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRecommendations: () => (/* binding */ useRecommendations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\n\n\n\nconst useRecommendations = ()=>{\n    const { data, loading, read } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL);\n    const { create } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL);\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const getRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getRecommendations]\": async function(type, limit) {\n            let autoGenerate = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n            if (status === 'authenticated') {\n                try {\n                    const params = new URLSearchParams();\n                    if (type && type !== 'all') params.append('type', type);\n                    if (limit) params.append('limit', limit.toString());\n                    const url = \"/api/v1/users/recommendations/\".concat(params.toString() ? '?' + params.toString() : '');\n                    const response = await read(url);\n                    // If no recommendations found and autoGenerate is true, generate them\n                    if ((!response || Array.isArray(response) && response.length === 0) && autoGenerate) {\n                        console.log('No recommendations found, generating new ones...');\n                        await generateRecommendations();\n                        // Fetch again after generation\n                        const newResponse = await read(url);\n                        const newRecommendations = Array.isArray(newResponse) ? newResponse : [];\n                        setRecommendations(newRecommendations);\n                        return newRecommendations;\n                    }\n                    const recommendations = Array.isArray(response) ? response : [];\n                    setRecommendations(recommendations);\n                    return response;\n                } catch (error) {\n                    console.error('Error fetching recommendations:', error);\n                    setRecommendations([]);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecommendations.useCallback[getRecommendations]\"], [\n        status,\n        read\n    ]);\n    const getRecommendationsByType = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getRecommendationsByType]\": async (type, limit)=>{\n            if (status === 'authenticated' && type !== 'all') {\n                try {\n                    const params = limit ? \"?limit=\".concat(limit) : '';\n                    const response = await read(\"/api/v1/users/recommendations/\".concat(type, \"/\").concat(params));\n                    return response || [];\n                } catch (error) {\n                    console.error('Error fetching recommendations by type:', error);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecommendations.useCallback[getRecommendationsByType]\"], [\n        status,\n        read\n    ]);\n    const generateRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[generateRecommendations]\": async ()=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/recommendations/generate/', {});\n                    return response;\n                } catch (error) {\n                    console.error('Error generating recommendations:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecommendations.useCallback[generateRecommendations]\"], [\n        status,\n        create\n    ]);\n    const getProductRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getProductRecommendations]\": async (productId, limit)=>{\n            try {\n                const params = limit ? \"?limit=\".concat(limit) : '';\n                const response = await read(\"/api/v1/users/recommendations/for-product/\".concat(productId, \"/\").concat(params));\n                return response || [];\n            } catch (error) {\n                console.error('Error fetching product recommendations:', error);\n                return [];\n            }\n        }\n    }[\"useRecommendations.useCallback[getProductRecommendations]\"], [\n        read\n    ]);\n    const trackInteraction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[trackInteraction]\": async (productId, interactionType, metadata)=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/interactions/track/', {\n                        product_id: productId,\n                        interaction_type: interactionType,\n                        metadata: metadata || {}\n                    });\n                    return response;\n                } catch (error) {\n                    console.error('Error tracking interaction:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecommendations.useCallback[trackInteraction]\"], [\n        status,\n        create\n    ]);\n    return {\n        recommendations,\n        loading,\n        getRecommendations,\n        getRecommendationsByType,\n        generateRecommendations,\n        getProductRecommendations,\n        trackInteraction\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2hvb2tzL3VzZVJlY29tbWVuZGF0aW9ucy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThDO0FBQ0Q7QUFDZjtBQUNjO0FBcUJyQyxNQUFNSyxxQkFBcUI7SUFDaEMsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRUMsSUFBSSxFQUFFLEdBQUdMLG1EQUFNQSxDQUFDQyxvREFBUUE7SUFDL0MsTUFBTSxFQUFFSyxNQUFNLEVBQUUsR0FBR04sbURBQU1BLENBQUNDLG9EQUFRQTtJQUNsQyxNQUFNLEVBQUVNLE1BQU0sRUFBRSxHQUFHUiwyREFBVUE7SUFDN0IsTUFBTSxDQUFDUyxpQkFBaUJDLG1CQUFtQixHQUFHWCwrQ0FBUUEsQ0FBdUIsRUFBRTtJQUUvRSxNQUFNWSxxQkFBcUJiLGtEQUFXQTs4REFBQyxlQUFPYyxNQUEyQkM7Z0JBQWdCQyxnRkFBd0I7WUFDL0csSUFBSU4sV0FBVyxpQkFBaUI7Z0JBQzlCLElBQUk7b0JBQ0YsTUFBTU8sU0FBUyxJQUFJQztvQkFDbkIsSUFBSUosUUFBUUEsU0FBUyxPQUFPRyxPQUFPRSxNQUFNLENBQUMsUUFBUUw7b0JBQ2xELElBQUlDLE9BQU9FLE9BQU9FLE1BQU0sQ0FBQyxTQUFTSixNQUFNSyxRQUFRO29CQUVoRCxNQUFNQyxNQUFNLGlDQUFrRixPQUFqREosT0FBT0csUUFBUSxLQUFLLE1BQU1ILE9BQU9HLFFBQVEsS0FBSztvQkFDM0YsTUFBTUUsV0FBVyxNQUFNZCxLQUFLYTtvQkFFNUIsc0VBQXNFO29CQUN0RSxJQUFJLENBQUMsQ0FBQ0MsWUFBYUMsTUFBTUMsT0FBTyxDQUFDRixhQUFhQSxTQUFTRyxNQUFNLEtBQUssQ0FBQyxLQUFNVCxjQUFjO3dCQUNyRlUsUUFBUUMsR0FBRyxDQUFDO3dCQUNaLE1BQU1DO3dCQUNOLCtCQUErQjt3QkFDL0IsTUFBTUMsY0FBYyxNQUFNckIsS0FBS2E7d0JBQy9CLE1BQU1TLHFCQUFxQlAsTUFBTUMsT0FBTyxDQUFDSyxlQUFlQSxjQUFjLEVBQUU7d0JBQ3hFakIsbUJBQW1Ca0I7d0JBQ25CLE9BQU9BO29CQUNUO29CQUVBLE1BQU1uQixrQkFBa0JZLE1BQU1DLE9BQU8sQ0FBQ0YsWUFBWUEsV0FBVyxFQUFFO29CQUMvRFYsbUJBQW1CRDtvQkFDbkIsT0FBT1c7Z0JBQ1QsRUFBRSxPQUFPUyxPQUFPO29CQUNkTCxRQUFRSyxLQUFLLENBQUMsbUNBQW1DQTtvQkFDakRuQixtQkFBbUIsRUFBRTtvQkFDckIsT0FBTyxFQUFFO2dCQUNYO1lBQ0Y7WUFDQSxPQUFPLEVBQUU7UUFDWDs2REFBRztRQUFDRjtRQUFRRjtLQUFLO0lBRWpCLE1BQU13QiwyQkFBMkJoQyxrREFBV0E7b0VBQUMsT0FBT2MsTUFBMEJDO1lBQzVFLElBQUlMLFdBQVcsbUJBQW1CSSxTQUFTLE9BQU87Z0JBQ2hELElBQUk7b0JBQ0YsTUFBTUcsU0FBU0YsUUFBUSxVQUFnQixPQUFOQSxTQUFVO29CQUMzQyxNQUFNTyxXQUFXLE1BQU1kLEtBQUssaUNBQXlDUyxPQUFSSCxNQUFLLEtBQVUsT0FBUEc7b0JBQ3JFLE9BQU9LLFlBQVksRUFBRTtnQkFDdkIsRUFBRSxPQUFPUyxPQUFPO29CQUNkTCxRQUFRSyxLQUFLLENBQUMsMkNBQTJDQTtvQkFDekQsT0FBTyxFQUFFO2dCQUNYO1lBQ0Y7WUFDQSxPQUFPLEVBQUU7UUFDWDttRUFBRztRQUFDckI7UUFBUUY7S0FBSztJQUVqQixNQUFNb0IsMEJBQTBCNUIsa0RBQVdBO21FQUFDO1lBQzFDLElBQUlVLFdBQVcsaUJBQWlCO2dCQUM5QixJQUFJO29CQUNGLE1BQU1ZLFdBQVcsTUFBTWIsT0FBTywyQ0FBMkMsQ0FBQztvQkFDMUUsT0FBT2E7Z0JBQ1QsRUFBRSxPQUFPUyxPQUFPO29CQUNkTCxRQUFRSyxLQUFLLENBQUMscUNBQXFDQTtvQkFDbkQsT0FBTztnQkFDVDtZQUNGO1FBQ0Y7a0VBQUc7UUFBQ3JCO1FBQVFEO0tBQU87SUFFbkIsTUFBTXdCLDRCQUE0QmpDLGtEQUFXQTtxRUFBQyxPQUFPa0MsV0FBbUJuQjtZQUN0RSxJQUFJO2dCQUNGLE1BQU1FLFNBQVNGLFFBQVEsVUFBZ0IsT0FBTkEsU0FBVTtnQkFDM0MsTUFBTU8sV0FBVyxNQUFNZCxLQUFLLDZDQUEwRFMsT0FBYmlCLFdBQVUsS0FBVSxPQUFQakI7Z0JBQ3RGLE9BQU9LLFlBQVksRUFBRTtZQUN2QixFQUFFLE9BQU9TLE9BQU87Z0JBQ2RMLFFBQVFLLEtBQUssQ0FBQywyQ0FBMkNBO2dCQUN6RCxPQUFPLEVBQUU7WUFDWDtRQUNGO29FQUFHO1FBQUN2QjtLQUFLO0lBRVQsTUFBTTJCLG1CQUFtQm5DLGtEQUFXQTs0REFBQyxPQUNuQ2tDLFdBQ0FFLGlCQUNBQztZQUVBLElBQUkzQixXQUFXLGlCQUFpQjtnQkFDOUIsSUFBSTtvQkFDRixNQUFNWSxXQUFXLE1BQU1iLE9BQU8scUNBQXFDO3dCQUNqRTZCLFlBQVlKO3dCQUNaSyxrQkFBa0JIO3dCQUNsQkMsVUFBVUEsWUFBWSxDQUFDO29CQUN6QjtvQkFDQSxPQUFPZjtnQkFDVCxFQUFFLE9BQU9TLE9BQU87b0JBQ2RMLFFBQVFLLEtBQUssQ0FBQywrQkFBK0JBO29CQUM3QyxPQUFPO2dCQUNUO1lBQ0Y7UUFDRjsyREFBRztRQUFDckI7UUFBUUQ7S0FBTztJQUVuQixPQUFPO1FBQ0xFO1FBQ0FKO1FBQ0FNO1FBQ0FtQjtRQUNBSjtRQUNBSztRQUNBRTtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcaG9va3NcXHVzZVJlY29tbWVuZGF0aW9ucy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDYWxsYmFjaywgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB1c2VBcGkgZnJvbSAnLi91c2VBcGknO1xuaW1wb3J0IHsgTUFJTl9VUkwgfSBmcm9tICcuLi9jb25zdGFudC91cmxzJztcblxuZXhwb3J0IGludGVyZmFjZSBSZWNvbW1lbmRlZFByb2R1Y3Qge1xuICBpZDogbnVtYmVyO1xuICBuYW1lOiBzdHJpbmc7XG4gIHNsdWc6IHN0cmluZztcbiAgcHJpY2U6IG51bWJlcjtcbiAgaW1hZ2VzOiBBcnJheTx7IGltYWdlOiBzdHJpbmcgfT47XG4gIGJyYW5kPzogeyBuYW1lOiBzdHJpbmcgfTtcbiAgY2F0ZWdvcnk/OiB7IG5hbWU6IHN0cmluZyB9O1xufVxuXG5leHBvcnQgdHlwZSBSZWNvbW1lbmRhdGlvblR5cGUgPSBcbiAgfCAnY2F0ZWdvcnlfYmFzZWQnIFxuICB8ICdicmFuZF9iYXNlZCcgXG4gIHwgJ3ByaWNlX2Jhc2VkJyBcbiAgfCAnY29sbGFib3JhdGl2ZScgXG4gIHwgJ2ZyZXF1ZW50bHlfYm91Z2h0JyBcbiAgfCAndHJlbmRpbmcnIFxuICB8ICdhbGwnO1xuXG5leHBvcnQgY29uc3QgdXNlUmVjb21tZW5kYXRpb25zID0gKCkgPT4ge1xuICBjb25zdCB7IGRhdGEsIGxvYWRpbmcsIHJlYWQgfSA9IHVzZUFwaShNQUlOX1VSTCk7XG4gIGNvbnN0IHsgY3JlYXRlIH0gPSB1c2VBcGkoTUFJTl9VUkwpO1xuICBjb25zdCB7IHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpO1xuICBjb25zdCBbcmVjb21tZW5kYXRpb25zLCBzZXRSZWNvbW1lbmRhdGlvbnNdID0gdXNlU3RhdGU8UmVjb21tZW5kZWRQcm9kdWN0W10+KFtdKTtcblxuICBjb25zdCBnZXRSZWNvbW1lbmRhdGlvbnMgPSB1c2VDYWxsYmFjayhhc3luYyAodHlwZT86IFJlY29tbWVuZGF0aW9uVHlwZSwgbGltaXQ/OiBudW1iZXIsIGF1dG9HZW5lcmF0ZTogYm9vbGVhbiA9IHRydWUpID0+IHtcbiAgICBpZiAoc3RhdHVzID09PSAnYXV0aGVudGljYXRlZCcpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgICAgICAgaWYgKHR5cGUgJiYgdHlwZSAhPT0gJ2FsbCcpIHBhcmFtcy5hcHBlbmQoJ3R5cGUnLCB0eXBlKTtcbiAgICAgICAgaWYgKGxpbWl0KSBwYXJhbXMuYXBwZW5kKCdsaW1pdCcsIGxpbWl0LnRvU3RyaW5nKCkpO1xuXG4gICAgICAgIGNvbnN0IHVybCA9IGAvYXBpL3YxL3VzZXJzL3JlY29tbWVuZGF0aW9ucy8ke3BhcmFtcy50b1N0cmluZygpID8gJz8nICsgcGFyYW1zLnRvU3RyaW5nKCkgOiAnJ31gO1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlYWQodXJsKTtcblxuICAgICAgICAvLyBJZiBubyByZWNvbW1lbmRhdGlvbnMgZm91bmQgYW5kIGF1dG9HZW5lcmF0ZSBpcyB0cnVlLCBnZW5lcmF0ZSB0aGVtXG4gICAgICAgIGlmICgoIXJlc3BvbnNlIHx8IChBcnJheS5pc0FycmF5KHJlc3BvbnNlKSAmJiByZXNwb25zZS5sZW5ndGggPT09IDApKSAmJiBhdXRvR2VuZXJhdGUpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnTm8gcmVjb21tZW5kYXRpb25zIGZvdW5kLCBnZW5lcmF0aW5nIG5ldyBvbmVzLi4uJyk7XG4gICAgICAgICAgYXdhaXQgZ2VuZXJhdGVSZWNvbW1lbmRhdGlvbnMoKTtcbiAgICAgICAgICAvLyBGZXRjaCBhZ2FpbiBhZnRlciBnZW5lcmF0aW9uXG4gICAgICAgICAgY29uc3QgbmV3UmVzcG9uc2UgPSBhd2FpdCByZWFkKHVybCk7XG4gICAgICAgICAgY29uc3QgbmV3UmVjb21tZW5kYXRpb25zID0gQXJyYXkuaXNBcnJheShuZXdSZXNwb25zZSkgPyBuZXdSZXNwb25zZSA6IFtdO1xuICAgICAgICAgIHNldFJlY29tbWVuZGF0aW9ucyhuZXdSZWNvbW1lbmRhdGlvbnMpO1xuICAgICAgICAgIHJldHVybiBuZXdSZWNvbW1lbmRhdGlvbnM7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCByZWNvbW1lbmRhdGlvbnMgPSBBcnJheS5pc0FycmF5KHJlc3BvbnNlKSA/IHJlc3BvbnNlIDogW107XG4gICAgICAgIHNldFJlY29tbWVuZGF0aW9ucyhyZWNvbW1lbmRhdGlvbnMpO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2U7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyByZWNvbW1lbmRhdGlvbnM6JywgZXJyb3IpO1xuICAgICAgICBzZXRSZWNvbW1lbmRhdGlvbnMoW10pO1xuICAgICAgICByZXR1cm4gW107XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBbXTtcbiAgfSwgW3N0YXR1cywgcmVhZF0pO1xuXG4gIGNvbnN0IGdldFJlY29tbWVuZGF0aW9uc0J5VHlwZSA9IHVzZUNhbGxiYWNrKGFzeW5jICh0eXBlOiBSZWNvbW1lbmRhdGlvblR5cGUsIGxpbWl0PzogbnVtYmVyKSA9PiB7XG4gICAgaWYgKHN0YXR1cyA9PT0gJ2F1dGhlbnRpY2F0ZWQnICYmIHR5cGUgIT09ICdhbGwnKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBwYXJhbXMgPSBsaW1pdCA/IGA/bGltaXQ9JHtsaW1pdH1gIDogJyc7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVhZChgL2FwaS92MS91c2Vycy9yZWNvbW1lbmRhdGlvbnMvJHt0eXBlfS8ke3BhcmFtc31gKTtcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlIHx8IFtdO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcmVjb21tZW5kYXRpb25zIGJ5IHR5cGU6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gW107XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBbXTtcbiAgfSwgW3N0YXR1cywgcmVhZF0pO1xuXG4gIGNvbnN0IGdlbmVyYXRlUmVjb21tZW5kYXRpb25zID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmIChzdGF0dXMgPT09ICdhdXRoZW50aWNhdGVkJykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjcmVhdGUoJy9hcGkvdjEvdXNlcnMvcmVjb21tZW5kYXRpb25zL2dlbmVyYXRlLycsIHt9KTtcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2VuZXJhdGluZyByZWNvbW1lbmRhdGlvbnM6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtzdGF0dXMsIGNyZWF0ZV0pO1xuXG4gIGNvbnN0IGdldFByb2R1Y3RSZWNvbW1lbmRhdGlvbnMgPSB1c2VDYWxsYmFjayhhc3luYyAocHJvZHVjdElkOiBudW1iZXIsIGxpbWl0PzogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBhcmFtcyA9IGxpbWl0ID8gYD9saW1pdD0ke2xpbWl0fWAgOiAnJztcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVhZChgL2FwaS92MS91c2Vycy9yZWNvbW1lbmRhdGlvbnMvZm9yLXByb2R1Y3QvJHtwcm9kdWN0SWR9LyR7cGFyYW1zfWApO1xuICAgICAgcmV0dXJuIHJlc3BvbnNlIHx8IFtdO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwcm9kdWN0IHJlY29tbWVuZGF0aW9uczonLCBlcnJvcik7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9LCBbcmVhZF0pO1xuXG4gIGNvbnN0IHRyYWNrSW50ZXJhY3Rpb24gPSB1c2VDYWxsYmFjayhhc3luYyAoXG4gICAgcHJvZHVjdElkOiBudW1iZXIsXG4gICAgaW50ZXJhY3Rpb25UeXBlOiBzdHJpbmcsXG4gICAgbWV0YWRhdGE/OiBhbnlcbiAgKSA9PiB7XG4gICAgaWYgKHN0YXR1cyA9PT0gJ2F1dGhlbnRpY2F0ZWQnKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNyZWF0ZSgnL2FwaS92MS91c2Vycy9pbnRlcmFjdGlvbnMvdHJhY2svJywge1xuICAgICAgICAgIHByb2R1Y3RfaWQ6IHByb2R1Y3RJZCxcbiAgICAgICAgICBpbnRlcmFjdGlvbl90eXBlOiBpbnRlcmFjdGlvblR5cGUsXG4gICAgICAgICAgbWV0YWRhdGE6IG1ldGFkYXRhIHx8IHt9XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2U7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB0cmFja2luZyBpbnRlcmFjdGlvbjonLCBlcnJvcik7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW3N0YXR1cywgY3JlYXRlXSk7XG5cbiAgcmV0dXJuIHtcbiAgICByZWNvbW1lbmRhdGlvbnMsXG4gICAgbG9hZGluZyxcbiAgICBnZXRSZWNvbW1lbmRhdGlvbnMsXG4gICAgZ2V0UmVjb21tZW5kYXRpb25zQnlUeXBlLFxuICAgIGdlbmVyYXRlUmVjb21tZW5kYXRpb25zLFxuICAgIGdldFByb2R1Y3RSZWNvbW1lbmRhdGlvbnMsXG4gICAgdHJhY2tJbnRlcmFjdGlvblxuICB9O1xufTtcbiJdLCJuYW1lcyI6WyJ1c2VDYWxsYmFjayIsInVzZVN0YXRlIiwidXNlU2Vzc2lvbiIsInVzZUFwaSIsIk1BSU5fVVJMIiwidXNlUmVjb21tZW5kYXRpb25zIiwiZGF0YSIsImxvYWRpbmciLCJyZWFkIiwiY3JlYXRlIiwic3RhdHVzIiwicmVjb21tZW5kYXRpb25zIiwic2V0UmVjb21tZW5kYXRpb25zIiwiZ2V0UmVjb21tZW5kYXRpb25zIiwidHlwZSIsImxpbWl0IiwiYXV0b0dlbmVyYXRlIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwiYXBwZW5kIiwidG9TdHJpbmciLCJ1cmwiLCJyZXNwb25zZSIsIkFycmF5IiwiaXNBcnJheSIsImxlbmd0aCIsImNvbnNvbGUiLCJsb2ciLCJnZW5lcmF0ZVJlY29tbWVuZGF0aW9ucyIsIm5ld1Jlc3BvbnNlIiwibmV3UmVjb21tZW5kYXRpb25zIiwiZXJyb3IiLCJnZXRSZWNvbW1lbmRhdGlvbnNCeVR5cGUiLCJnZXRQcm9kdWN0UmVjb21tZW5kYXRpb25zIiwicHJvZHVjdElkIiwidHJhY2tJbnRlcmFjdGlvbiIsImludGVyYWN0aW9uVHlwZSIsIm1ldGFkYXRhIiwicHJvZHVjdF9pZCIsImludGVyYWN0aW9uX3R5cGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useRecommendations.ts\n"));

/***/ })

});