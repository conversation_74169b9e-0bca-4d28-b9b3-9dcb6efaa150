"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx":
/*!*******************************************************!*\
  !*** ./components/product/RecentlyViewedProducts.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentlyViewedProducts: () => (/* binding */ RecentlyViewedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst RecentlyViewedProducts = (param)=>{\n    let { limit = 5, showTitle = true, className = \"\" } = param;\n    _s();\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_5__.MAIN_URL || '');\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentlyViewedProducts.useEffect\": ()=>{\n            const fetchRecentlyViewed = {\n                \"RecentlyViewedProducts.useEffect.fetchRecentlyViewed\": async ()=>{\n                    if (status === 'authenticated') {\n                        setLoading(true);\n                        try {\n                            const response = await read('/api/v1/users/recently-viewed/');\n                            const data = Array.isArray(response) ? response : [];\n                            setRecentlyViewed(data);\n                        } catch (error) {\n                            console.error('Error fetching recently viewed:', error);\n                            setRecentlyViewed([]);\n                        } finally{\n                            setLoading(false);\n                        }\n                    } else {\n                        setRecentlyViewed([]);\n                        setLoading(false);\n                    }\n                }\n            }[\"RecentlyViewedProducts.useEffect.fetchRecentlyViewed\"];\n            fetchRecentlyViewed();\n        }\n    }[\"RecentlyViewedProducts.useEffect\"], [\n        status\n    ]);\n    if (status !== 'authenticated' || !loading && !(recentlyViewed === null || recentlyViewed === void 0 ? void 0 : recentlyViewed.length)) {\n        return null;\n    }\n    const displayProducts = recentlyViewed.slice(0, limit);\n    // Transform products to match ProductType interface\n    const transformedProducts = displayProducts.map((product)=>{\n        var _product_images_, _product_images, _product_category, _product_brand;\n        return {\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            rating: 0,\n            image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n            category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name) || '',\n            brand: ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name) || '',\n            slug: product.slug\n        };\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"recently-viewed-products \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Recently Viewed\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"recently-viewed-products \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Recently Viewed\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                children: transformedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        ...product\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecentlyViewedProducts, \"mTUmrS71Lw6UdhvLZSSaqXuEnuY=\", false, function() {\n    return [\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = RecentlyViewedProducts;\nvar _c;\n$RefreshReg$(_c, \"RecentlyViewedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\n"));

/***/ })

});