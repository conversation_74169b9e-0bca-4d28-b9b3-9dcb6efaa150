"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/ProductRecommendations.tsx":
/*!*******************************************************!*\
  !*** ./components/product/ProductRecommendations.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductRecommendations: () => (/* binding */ ProductRecommendations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ProductRecommendations = (param)=>{\n    let { type = 'all', limit = 8, title, showTitle = true, className = \"\", layout = 'grid' } = param;\n    _s();\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_5__.MAIN_URL || '');\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductRecommendations.useEffect\": ()=>{\n            const fetchRecommendations = {\n                \"ProductRecommendations.useEffect.fetchRecommendations\": async ()=>{\n                    if (status === 'authenticated') {\n                        setLoading(true);\n                        try {\n                            let url = '/api/v1/users/recommendations/';\n                            if (type !== 'all') {\n                                url = \"/api/v1/users/recommendations/\".concat(type, \"/\");\n                            }\n                            if (limit) {\n                                url += \"?limit=\".concat(limit);\n                            }\n                            const data = await read(url);\n                            // Transform data to match ProductType interface\n                            const transformedData = Array.isArray(data) ? data.map({\n                                \"ProductRecommendations.useEffect.fetchRecommendations\": (product)=>{\n                                    var _product_images_, _product_images, _product_category, _product_brand;\n                                    return {\n                                        id: product.id,\n                                        name: product.name,\n                                        price: product.price,\n                                        rating: 0,\n                                        image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n                                        category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name) || '',\n                                        brand: ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name) || '',\n                                        slug: product.slug\n                                    };\n                                }\n                            }[\"ProductRecommendations.useEffect.fetchRecommendations\"]) : [];\n                            setRecommendations(transformedData);\n                        } catch (error) {\n                            console.error('Error fetching recommendations:', error);\n                            setRecommendations([]);\n                        } finally{\n                            setLoading(false);\n                        }\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"ProductRecommendations.useEffect.fetchRecommendations\"];\n            fetchRecommendations();\n        }\n    }[\"ProductRecommendations.useEffect\"], [\n        status,\n        type,\n        limit,\n        read\n    ]);\n    if (status !== 'authenticated' || !loading && !recommendations.length) {\n        return null;\n    }\n    const getTitle = ()=>{\n        if (title) return title;\n        const titles = {\n            category_based: 'Recommended for You',\n            brand_based: 'From Your Favorite Brands',\n            price_based: 'In Your Price Range',\n            collaborative: 'Customers Also Liked',\n            trending: 'Trending Now',\n            frequently_bought: 'Frequently Bought Together',\n            all: 'Recommended Products'\n        };\n        return titles[type] || 'Recommended Products';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"product-recommendations \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: getTitle()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 23\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderGrid = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n            children: recommendations.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    ...product\n                }, product.id + \"index\" + index, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 111,\n            columnNumber: 5\n        }, undefined);\n    const renderCarousel = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 pb-4\",\n                children: recommendations.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            ...product\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined)\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 119,\n            columnNumber: 5\n        }, undefined);\n    const renderList = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: recommendations.map((product)=>{\n                var _product_brand;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-4 p-4 border rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: product.image,\n                            alt: product.name,\n                            className: \"w-20 h-20 object-cover rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold\",\n                                    children: product.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: typeof product.brand === 'string' ? product.brand : (_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        product.price\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, product.id, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, undefined);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 131,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"product-recommendations \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: getTitle()\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, undefined),\n            layout === 'grid' && renderGrid(),\n            layout === 'carousel' && renderCarousel(),\n            layout === 'list' && renderList()\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductRecommendations, \"lWuYnvNiHf/xusyPtHRpqi5+3ps=\", false, function() {\n    return [\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = ProductRecommendations;\nvar _c;\n$RefreshReg$(_c, \"ProductRecommendations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/ProductRecommendations.tsx\n"));

/***/ })

});