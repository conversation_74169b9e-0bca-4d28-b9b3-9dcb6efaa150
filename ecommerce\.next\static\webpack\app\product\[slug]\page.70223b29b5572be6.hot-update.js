"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/RecommendationInitializer.tsx":
/*!**********************************************************!*\
  !*** ./components/product/RecommendationInitializer.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecommendationInitializer: () => (/* binding */ RecommendationInitializer),\n/* harmony export */   useRecommendationStatus: () => (/* binding */ useRecommendationStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecommendations */ \"(app-pages-browser)/./hooks/useRecommendations.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n/**\n * Component that automatically generates recommendations for authenticated users\n * This should be placed high in the component tree (like in layout or main app)\n */ const RecommendationInitializer = (param)=>{\n    let { children } = param;\n    _s();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const { generateRecommendations, getRecommendations } = (0,_hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations)();\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecommendationInitializer.useEffect\": ()=>{\n            const initializeRecommendations = {\n                \"RecommendationInitializer.useEffect.initializeRecommendations\": async ()=>{\n                    if (status === 'authenticated' && !hasInitialized && !isGenerating) {\n                        setIsGenerating(true);\n                        try {\n                            console.log('Checking for existing recommendations...');\n                            // Check if user already has recommendations\n                            const existingRecommendations = await getRecommendations('all', 5, false); // Don't auto-generate here\n                            if (!existingRecommendations || existingRecommendations.length === 0) {\n                                console.log('No existing recommendations found. Generating new recommendations...');\n                                // Generate recommendations in the background\n                                await generateRecommendations();\n                                console.log('Recommendations generated successfully!');\n                            } else {\n                                console.log(\"Found \".concat(existingRecommendations.length, \" existing recommendations\"));\n                            }\n                            setHasInitialized(true);\n                        } catch (error) {\n                            console.error('Error initializing recommendations:', error);\n                            // Don't block the app if recommendation generation fails\n                            setHasInitialized(true);\n                        } finally{\n                            setIsGenerating(false);\n                        }\n                    }\n                }\n            }[\"RecommendationInitializer.useEffect.initializeRecommendations\"];\n            // Add a small delay to avoid running immediately on page load\n            const timer = setTimeout(initializeRecommendations, 1000);\n            return ({\n                \"RecommendationInitializer.useEffect\": ()=>clearTimeout(timer)\n            })[\"RecommendationInitializer.useEffect\"];\n        }\n    }[\"RecommendationInitializer.useEffect\"], [\n        status,\n        hasInitialized,\n        isGenerating,\n        generateRecommendations,\n        getRecommendations\n    ]);\n    // Reset when user logs out\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecommendationInitializer.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                setHasInitialized(false);\n                setIsGenerating(false);\n            }\n        }\n    }[\"RecommendationInitializer.useEffect\"], [\n        status\n    ]);\n    // This component doesn't render anything visible\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n_s(RecommendationInitializer, \"9hUmVbr81j7bKa7ViOMyANKGKC4=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations\n    ];\n});\n_c = RecommendationInitializer;\n/**\n * Hook to check if recommendations are being generated\n */ const useRecommendationStatus = ()=>{\n    _s1();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const { getRecommendations } = (0,_hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations)();\n    const [hasRecommendations, setHasRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const checkRecommendations = async ()=>{\n        if (status === 'authenticated') {\n            setIsLoading(true);\n            try {\n                const recommendations = await getRecommendations('all', 1, false);\n                setHasRecommendations(recommendations && recommendations.length > 0);\n            } catch (error) {\n                console.error('Error checking recommendations:', error);\n                setHasRecommendations(false);\n            } finally{\n                setIsLoading(false);\n            }\n        } else {\n            setHasRecommendations(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useRecommendationStatus.useEffect\": ()=>{\n            checkRecommendations();\n        }\n    }[\"useRecommendationStatus.useEffect\"], [\n        status\n    ]);\n    return {\n        hasRecommendations,\n        isLoading,\n        checkRecommendations\n    };\n};\n_s1(useRecommendationStatus, \"hiNHZegnC22EciLVKHUXwEK71HA=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"RecommendationInitializer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/RecommendationInitializer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./layout/MainHOF.tsx":
/*!****************************!*\
  !*** ./layout/MainHOF.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/ui/toaster */ \"(app-pages-browser)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_utils_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/utils/Footer */ \"(app-pages-browser)/./components/utils/Footer.tsx\");\n/* harmony import */ var _components_utils_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/utils/Navbar */ \"(app-pages-browser)/./components/utils/Navbar.tsx\");\n/* harmony import */ var _components_product_RecommendationInitializer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/product/RecommendationInitializer */ \"(app-pages-browser)/./components/product/RecommendationInitializer.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst MainHOFContent = (param)=>{\n    let { children } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const callbackUrl = searchParams.get(\"callbackUrl\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col bg-theme-homepage w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecommendationInitializer__WEBPACK_IMPORTED_MODULE_4__.RecommendationInitializer, {}, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-full mx-auto px-4 sm:px-6 lg:px-8 flex-grow\",\n                children: [\n                    Boolean(callbackUrl) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-3 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                            href: typeof callbackUrl === \"string\" ? callbackUrl === null || callbackUrl === void 0 ? void 0 : callbackUrl.slice(1) : \"/\",\n                            className: \"flex items-center gap-1 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Back\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, undefined),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_utils_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_1__.Toaster, {}, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MainHOFContent, \"a+DZx9DY26Zf8FVy1bxe3vp9l1w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams\n    ];\n});\n_c = MainHOFContent;\nconst MainHOF = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MainHOFContent, {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\layout\\\\MainHOF.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = MainHOF;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainHOF);\nvar _c, _c1;\n$RefreshReg$(_c, \"MainHOFContent\");\n$RefreshReg$(_c1, \"MainHOF\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./layout/MainHOF.tsx\n"));

/***/ })

});