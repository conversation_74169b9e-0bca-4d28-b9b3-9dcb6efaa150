"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var swiper_css_autoplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/autoplay */ \"(app-pages-browser)/./node_modules/swiper/modules/autoplay.css\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/product/RecentlyViewedProducts */ \"(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\");\n/* harmony import */ var _components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/product/ProductRecommendations */ \"(app-pages-browser)/./components/product/ProductRecommendations.tsx\");\n/* harmony import */ var _components_product_GenerateRecommendationsButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/product/GenerateRecommendationsButton */ \"(app-pages-browser)/./components/product/GenerateRecommendationsButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-theme-homepage w-full relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-[800px] bg-gradient-to-b from-theme-accent-primary/5 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/5 to-theme-accent-secondary/10 blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                categories: categories !== null && categories !== void 0 ? categories : [],\n                                variant: \"navigation\",\n                                showTitle: false,\n                                showViewAll: false,\n                                maxCategories: 12,\n                                accentColor: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"relative w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 mt-6 mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_9__.RecentlyViewedProducts, {\n                                            limit: 6,\n                                            showTitle: true,\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                        title: \"Featured Products\",\n                                        subtitle: \"Discover our handpicked selection of premium products\",\n                                        products: featuredProducts,\n                                        loading: futureProductLoading,\n                                        viewAllLink: \"/shop\",\n                                        accentColor: \"primary\",\n                                        columns: {\n                                            xs: 2,\n                                            sm: 2,\n                                            md: 3,\n                                            lg: 4,\n                                            xl: 4,\n                                            \"2xl\": 5\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: \"Recommended for You\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_GenerateRecommendationsButton__WEBPACK_IMPORTED_MODULE_11__.GenerateRecommendationsButton, {\n                                                        variant: \"outline\",\n                                                        onSuccess: ()=>window.location.reload()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_10__.ProductRecommendations, {\n                                                type: \"all\",\n                                                limit: 8,\n                                                showTitle: false,\n                                                className: \"mb-8\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_10__.ProductRecommendations, {\n                                            type: \"trending\",\n                                            limit: 6,\n                                            title: \"Trending Now\",\n                                            layout: \"carousel\",\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                        title: \"Discover Products\",\n                                        subtitle: \"Explore our most popular items\",\n                                        products: popularProducts,\n                                        loading: popularProductLoading,\n                                        viewAllLink: \"/shop\",\n                                        accentColor: \"secondary\",\n                                        columns: {\n                                            xs: 2,\n                                            sm: 2,\n                                            md: 3,\n                                            lg: 4,\n                                            xl: 4,\n                                            \"2xl\": 5\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                        title: \"Shop by Category\",\n                                        subtitle: \"Browse our collection by category\",\n                                        categories: categories || [],\n                                        accentColor: \"tertiary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                        title: \"Browse Products by Category\",\n                                        subtitle: \"Filter products by your favorite categories\",\n                                        categories: categories || [],\n                                        categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                        accentColor: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: categoryData.category.name,\n                                            products: categoryData.products,\n                                            loading: categoryData.loading,\n                                            viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                            accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"t1NL8oCH1x/iqJpW5zHQdihbFlM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/product/GenerateRecommendationsButton.tsx":
/*!**************************************************************!*\
  !*** ./components/product/GenerateRecommendationsButton.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GenerateRecommendationsButton: () => (/* binding */ GenerateRecommendationsButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecommendations */ \"(app-pages-browser)/./hooks/useRecommendations.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst GenerateRecommendationsButton = (param)=>{\n    let { onSuccess, className = \"\", variant = 'primary' } = param;\n    _s();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const { generateRecommendations } = (0,_hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastGenerated, setLastGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleGenerate = async ()=>{\n        if (status !== 'authenticated' || isGenerating) return;\n        setIsGenerating(true);\n        try {\n            console.log('Manually generating recommendations...');\n            const result = await generateRecommendations();\n            if (result) {\n                console.log('Recommendations generated successfully:', result);\n                setLastGenerated(new Date());\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            } else {\n                console.error('Failed to generate recommendations');\n            }\n        } catch (error) {\n            console.error('Error generating recommendations:', error);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    if (status !== 'authenticated') {\n        return null;\n    }\n    const getButtonClasses = ()=>{\n        const baseClasses = \"px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\";\n        switch(variant){\n            case 'primary':\n                return \"\".concat(baseClasses, \" bg-blue-600 text-white hover:bg-blue-700 disabled:hover:bg-blue-600\");\n            case 'secondary':\n                return \"\".concat(baseClasses, \" bg-gray-600 text-white hover:bg-gray-700 disabled:hover:bg-gray-600\");\n            case 'outline':\n                return \"\".concat(baseClasses, \" border-2 border-blue-600 text-blue-600 hover:bg-blue-50 disabled:hover:bg-transparent\");\n            default:\n                return \"\".concat(baseClasses, \" bg-blue-600 text-white hover:bg-blue-700\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"generate-recommendations \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleGenerate,\n                disabled: isGenerating,\n                className: getButtonClasses(),\n                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-current\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\GenerateRecommendationsButton.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\GenerateRecommendationsButton.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\GenerateRecommendationsButton.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, undefined),\n                        \"Generating...\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\GenerateRecommendationsButton.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, undefined) : 'Generate Recommendations'\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\GenerateRecommendationsButton.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            lastGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-gray-500 mt-2\",\n                children: [\n                    \"Last generated: \",\n                    lastGenerated.toLocaleTimeString()\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\GenerateRecommendationsButton.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\GenerateRecommendationsButton.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GenerateRecommendationsButton, \"MkZkrJa/4PacvaAHMAkttQctsXc=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations\n    ];\n});\n_c = GenerateRecommendationsButton;\nvar _c;\n$RefreshReg$(_c, \"GenerateRecommendationsButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/GenerateRecommendationsButton.tsx\n"));

/***/ })

});