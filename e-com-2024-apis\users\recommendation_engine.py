"""
Advanced product recommendation engine for e-commerce platform.
Uses rule-based algorithms without ML dependencies for high performance.
"""

from typing import List, Dict, Tuple, Optional
from django.db.models import Count, Q, Avg, F, Case, When, Value, IntegerField
from django.contrib.auth import get_user_model
from products.models import Product, Category, Brand
from orders.models import Order, OrderItem
from .models import (
    RecentlyViewedProduct,
    ProductRecommendation,
    UserProductInteraction
)
import logging
from decimal import Decimal
from datetime import datetime, timedelta
from django.utils import timezone

logger = logging.getLogger(__name__)
Customer = get_user_model()


class RecommendationEngine:
    """Advanced product recommendation engine using rule-based algorithms"""

    # Interaction weights for recommendation scoring
    INTERACTION_WEIGHTS = {
        'view': 1.0,
        'add_to_cart': 3.0,
        'purchase': 10.0,
        'wishlist_add': 2.0,
        'review': 5.0,
        'search': 1.5,
    }

    # Time decay factors (how much to reduce score based on age)
    TIME_DECAY_DAYS = {
        'recent': 7,      # Full weight
        'medium': 30,     # 70% weight
        'old': 90,        # 30% weight
        'very_old': 365,  # 10% weight
    }

    def __init__(self, user: Customer):
        self.user = user
        self.user_interactions = UserProductInteraction.objects.filter(user=user)
        self.recently_viewed = RecentlyViewedProduct.objects.filter(user=user)

    def generate_all_recommendations(self, limit_per_type: int = 10) -> Dict[str, List[Product]]:
        """Generate all types of recommendations for the user"""
        recommendations = {}

        try:
            recommendations['category_based'] = self.get_category_based_recommendations(limit_per_type)
            recommendations['brand_based'] = self.get_brand_based_recommendations(limit_per_type)
            recommendations['price_based'] = self.get_price_based_recommendations(limit_per_type)
            recommendations['collaborative'] = self.get_collaborative_recommendations(limit_per_type)
            recommendations['frequently_bought'] = self.get_frequently_bought_together(limit_per_type)
            recommendations['trending'] = self.get_trending_products(limit_per_type)

            # Store recommendations in database for caching
            self._store_recommendations(recommendations)

        except Exception as e:
            logger.error(f"Error generating recommendations for user {self.user.id}: {e}")

        return recommendations

    def get_category_based_recommendations(self, limit: int = 10) -> List[Product]:
        """Recommend products from categories user has shown interest in"""
        # Get user's preferred categories based on interactions
        category_scores = {}

        for interaction in self.user_interactions.select_related('product__category'):
            if interaction.product.category:
                category = interaction.product.category
                weight = self.INTERACTION_WEIGHTS.get(interaction.interaction_type, 1.0)
                time_weight = self._calculate_time_weight(interaction.created_at)

                if category.id not in category_scores:
                    category_scores[category.id] = 0
                category_scores[category.id] += weight * time_weight

        # Get top categories
        top_categories = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)[:5]

        if not top_categories:
            return []

        # Get products from top categories, excluding already interacted products
        interacted_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        recommended_products = []
        for category_id, score in top_categories:
            products = Product.objects.filter(
                category_id=category_id,
                is_active=True
            ).exclude(
                id__in=interacted_product_ids
            ).order_by('-created_at')[:limit//len(top_categories) + 1]

            recommended_products.extend(products)

        return recommended_products[:limit]

    def get_brand_based_recommendations(self, limit: int = 10) -> List[Product]:
        """Recommend products from brands user has shown interest in"""
        # Similar logic to category-based but for brands
        brand_scores = {}

        for interaction in self.user_interactions.select_related('product__brand'):
            if interaction.product.brand:
                brand = interaction.product.brand
                weight = self.INTERACTION_WEIGHTS.get(interaction.interaction_type, 1.0)
                time_weight = self._calculate_time_weight(interaction.created_at)

                if brand.id not in brand_scores:
                    brand_scores[brand.id] = 0
                brand_scores[brand.id] += weight * time_weight

        top_brands = sorted(brand_scores.items(), key=lambda x: x[1], reverse=True)[:3]

        if not top_brands:
            return []

        interacted_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        recommended_products = []
        for brand_id, score in top_brands:
            products = Product.objects.filter(
                brand_id=brand_id,
                is_active=True
            ).exclude(
                id__in=interacted_product_ids
            ).order_by('-created_at')[:limit//len(top_brands) + 1]

            recommended_products.extend(products)

        return recommended_products[:limit]

    def get_price_based_recommendations(self, limit: int = 10) -> List[Product]:
        """Recommend products in user's preferred price range"""
        # Calculate user's price preferences
        user_purchases = self.user_interactions.filter(
            interaction_type='purchase'
        ).select_related('product')

        if not user_purchases.exists():
            # Fallback to viewed products
            user_purchases = self.user_interactions.filter(
                interaction_type='view'
            ).select_related('product')

        if not user_purchases.exists():
            return []

        prices = [interaction.product.price for interaction in user_purchases]
        avg_price = sum(prices) / len(prices)

        # Define price range (±30% of average)
        price_min = avg_price * Decimal('0.7')
        price_max = avg_price * Decimal('1.3')

        interacted_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        return Product.objects.filter(
            price__gte=price_min,
            price__lte=price_max,
            is_active=True
        ).exclude(
            id__in=interacted_product_ids
        ).order_by('?')[:limit]  # Random order for diversity

    def get_collaborative_recommendations(self, limit: int = 10) -> List[Product]:
        """Recommend products based on similar users' behavior"""
        # Find users with similar interaction patterns
        user_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        if len(user_product_ids) < 3:
            return []  # Need minimum interactions for collaborative filtering

        # Find users who interacted with similar products
        similar_users = Customer.objects.filter(
            product_interactions__product_id__in=user_product_ids
        ).exclude(
            id=self.user.id
        ).annotate(
            common_products=Count('product_interactions__product_id')
        ).filter(
            common_products__gte=2  # At least 2 common products
        ).order_by('-common_products')[:20]  # Top 20 similar users

        # Get products these similar users interacted with
        similar_user_products = UserProductInteraction.objects.filter(
            user__in=similar_users,
            interaction_type__in=['purchase', 'add_to_cart', 'wishlist_add']
        ).exclude(
            product_id__in=user_product_ids
        ).values('product_id').annotate(
            interaction_count=Count('id')
        ).order_by('-interaction_count')

        product_ids = [item['product_id'] for item in similar_user_products[:limit]]

        return Product.objects.filter(
            id__in=product_ids,
            is_active=True
        ).order_by('?')[:limit]

    def get_frequently_bought_together(self, limit: int = 10) -> List[Product]:
        """Recommend products frequently bought together with user's purchases"""
        # Get user's purchased products
        user_purchases = self.user_interactions.filter(
            interaction_type='purchase'
        ).values_list('product_id', flat=True)

        if not user_purchases:
            return []

        # Find products frequently bought together
        frequently_together = OrderItem.objects.filter(
            order__items__product_id__in=user_purchases
        ).exclude(
            product_id__in=user_purchases
        ).values('product_id').annotate(
            frequency=Count('order_id', distinct=True)
        ).filter(
            frequency__gte=2  # Bought together at least twice
        ).order_by('-frequency')

        product_ids = [item['product_id'] for item in frequently_together[:limit]]

        return Product.objects.filter(
            id__in=product_ids,
            is_active=True
        )[:limit]

    def get_trending_products(self, limit: int = 10) -> List[Product]:
        """Get currently trending products based on recent interactions"""
        # Products with high interaction volume in last 7 days
        week_ago = timezone.now() - timedelta(days=7)

        trending = UserProductInteraction.objects.filter(
            created_at__gte=week_ago
        ).values('product_id').annotate(
            interaction_score=Count('id') * 1.0 +  # Base interactions
            Count('id', filter=Q(interaction_type='purchase')) * 5.0 +  # Purchases weighted more
            Count('id', filter=Q(interaction_type='add_to_cart')) * 2.0  # Cart adds weighted more
        ).order_by('-interaction_score')

        # Exclude products user already interacted with
        interacted_product_ids = set(
            self.user_interactions.values_list('product_id', flat=True)
        )

        trending_product_ids = [
            item['product_id'] for item in trending
            if item['product_id'] not in interacted_product_ids
        ][:limit]

        return Product.objects.filter(
            id__in=trending_product_ids,
            is_active=True
        )[:limit]

    def _calculate_time_weight(self, interaction_date: datetime) -> float:
        """Calculate time-based weight for interactions"""
        days_ago = (timezone.now() - interaction_date).days

        if days_ago <= self.TIME_DECAY_DAYS['recent']:
            return 1.0
        elif days_ago <= self.TIME_DECAY_DAYS['medium']:
            return 0.7
        elif days_ago <= self.TIME_DECAY_DAYS['old']:
            return 0.3
        elif days_ago <= self.TIME_DECAY_DAYS['very_old']:
            return 0.1
        else:
            return 0.05  # Very old interactions have minimal weight

    def _store_recommendations(self, recommendations: Dict[str, List[Product]]):
        """Store generated recommendations in database for caching"""
        # Clear existing recommendations for this user
        ProductRecommendation.objects.filter(user=self.user).delete()

        recommendation_objects = []
        for rec_type, products in recommendations.items():
            for i, product in enumerate(products):
                score = 1.0 - (i * 0.1)  # Decreasing score based on position
                recommendation_objects.append(
                    ProductRecommendation(
                        user=self.user,
                        product=product,
                        recommendation_type=rec_type,
                        score=max(score, 0.1)  # Minimum score of 0.1
                    )
                )

        if recommendation_objects:
            ProductRecommendation.objects.bulk_create(recommendation_objects)

    @classmethod
    def track_interaction(cls, user: Customer, product: Product,
                         interaction_type: str, session_id: str = None,
                         metadata: dict = None):
        """Track user interaction with a product"""
        UserProductInteraction.objects.create(
            user=user,
            product=product,
            interaction_type=interaction_type,
            interaction_weight=cls.INTERACTION_WEIGHTS.get(interaction_type, 1.0),
            session_id=session_id,
            metadata=metadata or {}
        )

    def get_smart_similar_products(self, product: Product, limit: int = 10) -> List[Product]:
        """Get similar products using multiple rule-based criteria"""
        similar_products = Product.objects.filter(is_active=True).exclude(id=product.id)

        # Multi-criteria scoring without ML
        similar_products = similar_products.annotate(
            similarity_score=Case(
                # Same category and brand = highest score
                When(Q(category=product.category) & Q(brand=product.brand), then=Value(100)),
                # Same category, different brand = high score
                When(category=product.category, then=Value(80)),
                # Same brand, different category = medium score
                When(brand=product.brand, then=Value(60)),
                # Similar price range (±50%) = low score
                When(
                    Q(price__gte=product.price * Decimal('0.5')) &
                    Q(price__lte=product.price * Decimal('1.5')),
                    then=Value(40)
                ),
                default=Value(0),
                output_field=IntegerField()
            )
        ).filter(
            similarity_score__gt=0
        ).order_by('-similarity_score', '?')[:limit]

        return list(similar_products)

    @classmethod
    def cleanup_old_interactions(cls, days: int = 365):
        """Clean up old interactions to maintain performance"""
        cutoff_date = timezone.now() - timedelta(days=days)
        deleted_count = UserProductInteraction.objects.filter(
            created_at__lt=cutoff_date
        ).delete()[0]
        logger.info(f"Cleaned up {deleted_count} old interactions")
        return deleted_count

    @classmethod
    def cleanup_old_recommendations(cls, days: int = 30):
        """Clean up old recommendations to maintain performance"""
        cutoff_date = timezone.now() - timedelta(days=days)
        deleted_count = ProductRecommendation.objects.filter(
            created_at__lt=cutoff_date
        ).delete()[0]
        logger.info(f"Cleaned up {deleted_count} old recommendations")
        return deleted_count
