import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { RecommendationType } from '../../hooks/useRecommendations';
import ProductCard from './ProductCard';
import { ProductType } from '../../types/product.d';
import useApi from '../../hooks/useApi';
import { MAIN_URL } from '../../constant/urls';

interface ProductRecommendationsProps {
  type?: RecommendationType;
  limit?: number;
  title?: string;
  showTitle?: boolean;
  className?: string;
  layout?: 'grid' | 'carousel' | 'list';
}

export const ProductRecommendations: React.FC<ProductRecommendationsProps> = ({
  type = 'all',
  limit = 8,
  title,
  showTitle = true,
  className = "",
  layout = 'grid'
}) => {
  const [recommendations, setRecommendations] = useState<ProductType[]>([]);
  const [loading, setLoading] = useState(true);
  const { read } = useApi(MAIN_URL || '');
  const { status } = useSession();

  useEffect(() => {
    const fetchRecommendations = async () => {
      if (status === 'authenticated') {
        setLoading(true);
        try {
          let url = '/api/v1/users/recommendations/';
          if (type !== 'all') {
            url = `/api/v1/users/recommendations/${type}/`;
          }
          if (limit) {
            url += `?limit=${limit}`;
          }

          const data = await read(url);

          // Transform data to match ProductType interface
          const transformedData: ProductType[] = Array.isArray(data) ? data.map((product: any) => ({
            id: product.id,
            name: product.name,
            price: product.price,
            rating: 0,
            image: product.images?.[0]?.image || '/placeholder.jpg',
            category: product.category?.name || '',
            brand: product.brand?.name || '',
            slug: product.slug
          })) : [];

          setRecommendations(transformedData);
        } catch (error) {
          console.error('Error fetching recommendations:', error);
          setRecommendations([]);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [status, type, limit, read]);

  if (status !== 'authenticated' || (!loading && !recommendations.length)) {
    return null;
  }

  const getTitle = () => {
    if (title) return title;

    const titles = {
      category_based: 'Recommended for You',
      brand_based: 'From Your Favorite Brands',
      price_based: 'In Your Price Range',
      collaborative: 'Customers Also Liked',
      trending: 'Trending Now',
      frequently_bought: 'Frequently Bought Together',
      all: 'Recommended Products'
    };

    return titles[type] || 'Recommended Products';
  };

  if (loading) {
    return (
      <div className={`product-recommendations ${className}`}>
        {showTitle && <h3 className="text-lg font-semibold mb-4">{getTitle()}</h3>}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array.from({ length: limit }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-300 h-48 rounded-lg mb-2"></div>
              <div className="bg-gray-300 h-4 rounded mb-1"></div>
              <div className="bg-gray-300 h-4 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const renderGrid = () => (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {recommendations.map((product,index) => (
        <ProductCard key={product.id+"index"+index} {...product} />
      ))}
    </div>
  );

  const renderCarousel = () => (
    <div className="overflow-x-auto">
      <div className="flex space-x-4 pb-4">
        {recommendations.map((product) => (
          <div key={product.id} className="flex-shrink-0 w-64">
            <ProductCard {...product} />
          </div>
        ))}
      </div>
    </div>
  );

  const renderList = () => (
    <div className="space-y-4">
      {recommendations.map((product) => (
        <div key={product.id} className="flex space-x-4 p-4 border rounded-lg">
          <img
            src={product.image}
            alt={product.name}
            className="w-20 h-20 object-cover rounded"
          />
          <div className="flex-1">
            <h4 className="font-semibold">{product.name}</h4>
            <p className="text-gray-600">{typeof product.brand === 'string' ? product.brand : product.brand?.name}</p>
            <p className="text-lg font-bold">₹{product.price}</p>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className={`product-recommendations ${className}`}>
      {showTitle && (
        <h3 className="text-lg font-semibold mb-4">{getTitle()}</h3>
      )}
      {layout === 'grid' && renderGrid()}
      {layout === 'carousel' && renderCarousel()}
      {layout === 'list' && renderList()}
    </div>
  );
};
