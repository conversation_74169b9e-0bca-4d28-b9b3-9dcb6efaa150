"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/SmartRelatedProducts.tsx":
/*!*****************************************************!*\
  !*** ./components/product/SmartRelatedProducts.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartRelatedProducts: () => (/* binding */ SmartRelatedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useRecommendations */ \"(app-pages-browser)/./hooks/useRecommendations.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SmartRelatedProducts = (param)=>{\n    let { productId, categorySlug, limit = 8, className = \"\" } = param;\n    _s();\n    const [relatedProducts, setRelatedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { getProductRecommendations } = (0,_hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_2__.useRecommendations)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartRelatedProducts.useEffect\": ()=>{\n            const fetchRelatedProducts = {\n                \"SmartRelatedProducts.useEffect.fetchRelatedProducts\": async ()=>{\n                    setLoading(true);\n                    try {\n                        // Try to get smart recommendations first\n                        const smartRecommendations = await getProductRecommendations(productId, limit);\n                        if (smartRecommendations && smartRecommendations.length > 0) {\n                            setRelatedProducts(smartRecommendations);\n                        } else if (categorySlug) {\n                            var _response_data_results, _response_data;\n                            // Fallback to category-based products\n                            const endpoint = (0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(categorySlug);\n                            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL).concat(endpoint));\n                            const categoryProducts = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_results = _response_data.results) === null || _response_data_results === void 0 ? void 0 : _response_data_results.products) || [];\n                            // Filter out current product\n                            const filtered = categoryProducts.filter({\n                                \"SmartRelatedProducts.useEffect.fetchRelatedProducts.filtered\": (p)=>p.id !== productId\n                            }[\"SmartRelatedProducts.useEffect.fetchRelatedProducts.filtered\"]);\n                            setRelatedProducts(filtered.slice(0, limit));\n                        }\n                    } catch (error) {\n                        console.error('Error fetching related products:', error);\n                        setRelatedProducts([]);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SmartRelatedProducts.useEffect.fetchRelatedProducts\"];\n            if (productId) {\n                fetchRelatedProducts();\n            }\n        }\n    }[\"SmartRelatedProducts.useEffect\"], [\n        productId,\n        categorySlug,\n        limit,\n        getProductRecommendations\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-16 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-6\",\n                    children: \"Related Products\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: Array.from({\n                        length: 4\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!relatedProducts.length) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-16 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Related Products\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: relatedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        ...product\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SmartRelatedProducts, \"TnmWAccyt3KJQ+AkJVNp1uixaa8=\", false, function() {\n    return [\n        _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_2__.useRecommendations\n    ];\n});\n_c = SmartRelatedProducts;\nvar _c;\n$RefreshReg$(_c, \"SmartRelatedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/SmartRelatedProducts.tsx\n"));

/***/ })

});