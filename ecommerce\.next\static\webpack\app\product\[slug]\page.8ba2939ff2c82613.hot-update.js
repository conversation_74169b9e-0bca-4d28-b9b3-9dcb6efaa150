"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/SmartRelatedProducts.tsx":
/*!*****************************************************!*\
  !*** ./components/product/SmartRelatedProducts.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartRelatedProducts: () => (/* binding */ SmartRelatedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useRecommendations */ \"(app-pages-browser)/./hooks/useRecommendations.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SmartRelatedProducts = (param)=>{\n    let { productId, categorySlug, limit = 8, className = \"\" } = param;\n    _s();\n    const [relatedProducts, setRelatedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { getProductRecommendations } = (0,_hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_2__.useRecommendations)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartRelatedProducts.useEffect\": ()=>{\n            const fetchRelatedProducts = {\n                \"SmartRelatedProducts.useEffect.fetchRelatedProducts\": async ()=>{\n                    setLoading(true);\n                    try {\n                        // Try to get smart recommendations first\n                        const smartRecommendations = await getProductRecommendations(productId, limit);\n                        if (Array.isArray(smartRecommendations) && smartRecommendations.length > 0) {\n                            // Transform smart recommendations to ProductType\n                            const transformedRecommendations = smartRecommendations.map({\n                                \"SmartRelatedProducts.useEffect.fetchRelatedProducts.transformedRecommendations\": (product)=>{\n                                    var _product_images_, _product_images, _product_category, _product_brand;\n                                    return {\n                                        id: product.id,\n                                        name: product.name,\n                                        price: product.price,\n                                        rating: 0,\n                                        image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n                                        category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name) || '',\n                                        brand: ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name) || '',\n                                        slug: product.slug\n                                    };\n                                }\n                            }[\"SmartRelatedProducts.useEffect.fetchRelatedProducts.transformedRecommendations\"]);\n                            setRelatedProducts(transformedRecommendations);\n                        } else if (categorySlug) {\n                            var _response_data_results, _response_data;\n                            // Fallback to category-based products\n                            const endpoint = (0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(categorySlug);\n                            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL).concat(endpoint));\n                            const categoryProducts = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_results = _response_data.results) === null || _response_data_results === void 0 ? void 0 : _response_data_results.products) || [];\n                            // Filter out current product and transform to ProductType\n                            const filtered = categoryProducts.filter({\n                                \"SmartRelatedProducts.useEffect.fetchRelatedProducts.filtered\": (p)=>p.id !== productId\n                            }[\"SmartRelatedProducts.useEffect.fetchRelatedProducts.filtered\"]);\n                            const transformedProducts = filtered.slice(0, limit).map({\n                                \"SmartRelatedProducts.useEffect.fetchRelatedProducts.transformedProducts\": (product)=>{\n                                    var _product_images_, _product_images, _product_category, _product_brand;\n                                    return {\n                                        id: product.id,\n                                        name: product.name,\n                                        price: product.price,\n                                        rating: product.rating || 0,\n                                        image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n                                        category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name) || '',\n                                        brand: ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name) || '',\n                                        slug: product.slug\n                                    };\n                                }\n                            }[\"SmartRelatedProducts.useEffect.fetchRelatedProducts.transformedProducts\"]);\n                            setRelatedProducts(transformedProducts);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching related products:', error);\n                        setRelatedProducts([]);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SmartRelatedProducts.useEffect.fetchRelatedProducts\"];\n            if (productId) {\n                fetchRelatedProducts();\n            }\n        }\n    }[\"SmartRelatedProducts.useEffect\"], [\n        productId,\n        categorySlug,\n        limit,\n        getProductRecommendations\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-16 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-6\",\n                    children: \"Related Products\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: Array.from({\n                        length: 4\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!relatedProducts.length) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-16 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Related Products\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: relatedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        ...product\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\SmartRelatedProducts.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SmartRelatedProducts, \"TnmWAccyt3KJQ+AkJVNp1uixaa8=\", false, function() {\n    return [\n        _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_2__.useRecommendations\n    ];\n});\n_c = SmartRelatedProducts;\nvar _c;\n$RefreshReg$(_c, \"SmartRelatedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/SmartRelatedProducts.tsx\n"));

/***/ })

});