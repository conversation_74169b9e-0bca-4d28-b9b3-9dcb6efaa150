"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./hooks/useRecommendations.ts":
/*!*************************************!*\
  !*** ./hooks/useRecommendations.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRecommendations: () => (/* binding */ useRecommendations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\n\n\n\nconst useRecommendations = ()=>{\n    const { loading, read } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    const { create } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const getRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getRecommendations]\": async (type, limit)=>{\n            if (status === 'authenticated') {\n                try {\n                    const params = new URLSearchParams();\n                    if (type && type !== 'all') params.append('type', type);\n                    if (limit) params.append('limit', limit.toString());\n                    const url = \"/api/v1/users/recommendations/\".concat(params.toString() ? '?' + params.toString() : '');\n                    const response = await read(url);\n                    const data = Array.isArray(response) ? response : [];\n                    setRecommendations(data);\n                    return data;\n                } catch (error) {\n                    console.error('Error fetching recommendations:', error);\n                    setRecommendations([]);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecommendations.useCallback[getRecommendations]\"], [\n        status,\n        read\n    ]);\n    const getRecommendationsByType = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getRecommendationsByType]\": async (type, limit)=>{\n            if (status === 'authenticated' && type !== 'all') {\n                try {\n                    const params = limit ? \"?limit=\".concat(limit) : '';\n                    const response = await read(\"/api/v1/users/recommendations/\".concat(type, \"/\").concat(params));\n                    return Array.isArray(response) ? response : [];\n                } catch (error) {\n                    console.error('Error fetching recommendations by type:', error);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecommendations.useCallback[getRecommendationsByType]\"], [\n        status,\n        read\n    ]);\n    const generateRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[generateRecommendations]\": async ()=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/recommendations/generate/', {});\n                    return response;\n                } catch (error) {\n                    console.error('Error generating recommendations:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecommendations.useCallback[generateRecommendations]\"], [\n        status,\n        create\n    ]);\n    const getProductRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getProductRecommendations]\": async (productId, limit)=>{\n            try {\n                const params = limit ? \"?limit=\".concat(limit) : '';\n                const response = await read(\"/api/v1/users/recommendations/for-product/\".concat(productId, \"/\").concat(params));\n                return Array.isArray(response) ? response : [];\n            } catch (error) {\n                console.error('Error fetching product recommendations:', error);\n                return [];\n            }\n        }\n    }[\"useRecommendations.useCallback[getProductRecommendations]\"], [\n        read\n    ]);\n    const trackInteraction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[trackInteraction]\": async (productId, interactionType, metadata)=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/interactions/track/', {\n                        product_id: productId,\n                        interaction_type: interactionType,\n                        metadata: metadata || {}\n                    });\n                    return response;\n                } catch (error) {\n                    console.error('Error tracking interaction:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecommendations.useCallback[trackInteraction]\"], [\n        status,\n        create\n    ]);\n    return {\n        recommendations,\n        loading,\n        getRecommendations,\n        getRecommendationsByType,\n        generateRecommendations,\n        getProductRecommendations,\n        trackInteraction\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useRecommendations.ts\n"));

/***/ })

});