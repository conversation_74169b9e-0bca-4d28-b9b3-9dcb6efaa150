"use client";
import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { RecentlyViewedProducts } from '../../components/product/RecentlyViewedProducts';
import { ProductRecommendations } from '../../components/product/ProductRecommendations';
import { RecommendationRefresher } from '../../components/product/RecommendationRefresher';
import MainHOF from '../../layout/MainHOF';

const TestRecommendationsPage = () => {
  const { status } = useSession();
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRecommendationsUpdated = () => {
    // Force re-render of recommendation components
    setRefreshKey(prev => prev + 1);
  };

  if (status === 'loading') {
    return (
      <MainHOF>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">Loading...</div>
        </div>
      </MainHOF>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <MainHOF>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Test Recommendations</h1>
            <p>Please log in to test the recommendations feature.</p>
          </div>
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Test Recommendations & Recently Viewed</h1>
          <RecommendationRefresher
            onRecommendationsUpdated={handleRecommendationsUpdated}
            showButton={true}
          />
        </div>
        
        {/* Recently Viewed Section */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Recently Viewed Products</h2>
          <RecentlyViewedProducts
            limit={6}
            showTitle={false}
            className="border rounded-lg p-4"
          />
        </div>

        {/* All Recommendations */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">All Recommendations</h2>
          <ProductRecommendations
            key={`all-${refreshKey}`}
            type="all"
            limit={8}
            showTitle={false}
            className="border rounded-lg p-4"
          />
        </div>

        {/* Category Based Recommendations */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Category Based</h2>
          <ProductRecommendations
            key={`category-${refreshKey}`}
            type="category_based"
            limit={6}
            showTitle={false}
            className="border rounded-lg p-4"
          />
        </div>

        {/* Brand Based Recommendations */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Brand Based</h2>
          <ProductRecommendations
            key={`brand-${refreshKey}`}
            type="brand_based"
            limit={6}
            showTitle={false}
            className="border rounded-lg p-4"
          />
        </div>

        {/* Trending Products */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Trending Products</h2>
          <ProductRecommendations
            key={`trending-${refreshKey}`}
            type="trending"
            limit={6}
            showTitle={false}
            layout="carousel"
            className="border rounded-lg p-4"
          />
        </div>

        {/* Frequently Bought Together */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Frequently Bought Together</h2>
          <ProductRecommendations
            key={`frequently-${refreshKey}`}
            type="frequently_bought"
            limit={4}
            showTitle={false}
            layout="list"
            className="border rounded-lg p-4"
          />
        </div>

        {/* Instructions */}
        <div className="mt-16 p-6 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">How to Test Real-Time Recommendations:</h3>
          <ol className="list-decimal list-inside space-y-2">
            <li><strong>View Products:</strong> Visit product pages - they're automatically tracked</li>
            <li><strong>Add to Cart:</strong> Click "Add to Cart" on any product - triggers immediate recommendation generation</li>
            <li><strong>Wishlist Actions:</strong> Add/remove from wishlist - triggers recommendation updates</li>
            <li><strong>Manual Refresh:</strong> Click "Refresh Recommendations" button above</li>
            <li><strong>See Changes:</strong> Watch recommendations update in real-time after each action</li>
            <li><strong>Check Console:</strong> Open browser dev tools to see API calls and tracking</li>
          </ol>

          <div className="mt-4 p-4 bg-green-100 rounded">
            <h4 className="font-semibold text-green-800">✅ What's Working Now:</h4>
            <ul className="list-disc list-inside text-green-700 mt-2 space-y-1">
              <li>Automatic interaction tracking on all user actions</li>
              <li>Real-time recommendation generation after cart/wishlist actions</li>
              <li>Smart recommendation algorithms (category, brand, collaborative, etc.)</li>
              <li>Recently viewed products with automatic updates</li>
              <li>Frontend components that refresh automatically</li>
            </ul>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

export default TestRecommendationsPage;
