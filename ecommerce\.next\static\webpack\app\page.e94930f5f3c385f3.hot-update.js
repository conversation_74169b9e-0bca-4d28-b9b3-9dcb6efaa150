"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var swiper_css_autoplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/autoplay */ \"(app-pages-browser)/./node_modules/swiper/modules/autoplay.css\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/product/RecentlyViewedProducts */ \"(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\");\n/* harmony import */ var _components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/product/ProductRecommendations */ \"(app-pages-browser)/./components/product/ProductRecommendations.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-theme-homepage w-full relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-[800px] bg-gradient-to-b from-theme-accent-primary/5 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/5 to-theme-accent-secondary/10 blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                    categories: categories !== null && categories !== void 0 ? categories : [],\n                                    variant: \"navigation\",\n                                    showTitle: false,\n                                    showViewAll: false,\n                                    maxCategories: 12,\n                                    accentColor: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_9__.RecentlyViewedProducts, {}, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"relative w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 mt-6 mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_9__.RecentlyViewedProducts, {\n                                            limit: 6,\n                                            showTitle: true,\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                        title: \"Featured Products\",\n                                        subtitle: \"Discover our handpicked selection of premium products\",\n                                        products: featuredProducts,\n                                        loading: futureProductLoading,\n                                        viewAllLink: \"/shop\",\n                                        accentColor: \"primary\",\n                                        columns: {\n                                            xs: 2,\n                                            sm: 2,\n                                            md: 3,\n                                            lg: 4,\n                                            xl: 4,\n                                            \"2xl\": 5\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_10__.ProductRecommendations, {\n                                            type: \"all\",\n                                            limit: 8,\n                                            title: \"Recommended for You\",\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_10__.ProductRecommendations, {\n                                            type: \"trending\",\n                                            limit: 6,\n                                            title: \"Trending Now\",\n                                            layout: \"carousel\",\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                        title: \"Discover Products\",\n                                        subtitle: \"Explore our most popular items\",\n                                        products: popularProducts,\n                                        loading: popularProductLoading,\n                                        viewAllLink: \"/shop\",\n                                        accentColor: \"secondary\",\n                                        columns: {\n                                            xs: 2,\n                                            sm: 2,\n                                            md: 3,\n                                            lg: 4,\n                                            xl: 4,\n                                            \"2xl\": 5\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                        title: \"Shop by Category\",\n                                        subtitle: \"Browse our collection by category\",\n                                        categories: categories || [],\n                                        accentColor: \"tertiary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                        title: \"Browse Products by Category\",\n                                        subtitle: \"Filter products by your favorite categories\",\n                                        categories: categories || [],\n                                        categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                        accentColor: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: categoryData.category.name,\n                                            products: categoryData.products,\n                                            loading: categoryData.loading,\n                                            viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                            accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"t1NL8oCH1x/iqJpW5zHQdihbFlM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});