"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var swiper_css_autoplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/autoplay */ \"(app-pages-browser)/./node_modules/swiper/modules/autoplay.css\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/product/RecentlyViewedProducts */ \"(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\");\n/* harmony import */ var _components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/product/ProductRecommendations */ \"(app-pages-browser)/./components/product/ProductRecommendations.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-theme-homepage w-full relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-[800px] bg-gradient-to-b from-theme-accent-primary/5 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/5 to-theme-accent-secondary/10 blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                categories: categories !== null && categories !== void 0 ? categories : [],\n                                variant: \"navigation\",\n                                showTitle: false,\n                                showViewAll: false,\n                                maxCategories: 12,\n                                accentColor: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100 animate-pulse rounded-xl\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"relative w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 mt-6 mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_9__.RecentlyViewedProducts, {\n                                            limit: 6,\n                                            showTitle: true,\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                        title: \"Featured Products\",\n                                        subtitle: \"Discover our handpicked selection of premium products\",\n                                        products: featuredProducts,\n                                        loading: futureProductLoading,\n                                        viewAllLink: \"/shop\",\n                                        accentColor: \"primary\",\n                                        columns: {\n                                            xs: 2,\n                                            sm: 2,\n                                            md: 3,\n                                            lg: 4,\n                                            xl: 4,\n                                            \"2xl\": 5\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-8\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_10__.ProductRecommendations, {\n                                            type: \"trending\",\n                                            limit: 6,\n                                            title: \"Trending Now\",\n                                            layout: \"carousel\",\n                                            className: \"mb-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                        title: \"Discover Products\",\n                                        subtitle: \"Explore our most popular items\",\n                                        products: popularProducts,\n                                        loading: popularProductLoading,\n                                        viewAllLink: \"/shop\",\n                                        accentColor: \"secondary\",\n                                        columns: {\n                                            xs: 2,\n                                            sm: 2,\n                                            md: 3,\n                                            lg: 4,\n                                            xl: 4,\n                                            \"2xl\": 5\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                        title: \"Shop by Category\",\n                                        subtitle: \"Browse our collection by category\",\n                                        categories: categories || [],\n                                        accentColor: \"tertiary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                        title: \"Browse Products by Category\",\n                                        subtitle: \"Filter products by your favorite categories\",\n                                        categories: categories || [],\n                                        categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                        accentColor: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: categoryData.category.name,\n                                            products: categoryData.products,\n                                            loading: categoryData.loading,\n                                            viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                            accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"t1NL8oCH1x/iqJpW5zHQdihbFlM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});