globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/shop/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./components/privacy/ConsentBanner.tsx":{"*":{"id":"(ssr)/./components/privacy/ConsentBanner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/utils/JsonLdWrapper.tsx":{"*":{"id":"(ssr)/./components/utils/JsonLdWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./provider/AuthProvider.tsx":{"*":{"id":"(ssr)/./provider/AuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./provider/SecureCartProvider.tsx":{"*":{"id":"(ssr)/./provider/SecureCartProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/product/[slug]/page.tsx":{"*":{"id":"(ssr)/./app/product/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/login/page.tsx":{"*":{"id":"(ssr)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cart/page.tsx":{"*":{"id":"(ssr)/./app/cart/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/account/page.tsx":{"*":{"id":"(ssr)/./app/account/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/test-recommendations/page.tsx":{"*":{"id":"(ssr)/./app/test-recommendations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/shop/page.tsx":{"*":{"id":"(ssr)/./app/shop/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Triumph\\ecommerce\\components\\privacy\\ConsentBanner.tsx":{"id":"(app-pages-browser)/./components/privacy/ConsentBanner.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Triumph\\ecommerce\\components\\utils\\JsonLdWrapper.tsx":{"id":"(app-pages-browser)/./components/utils/JsonLdWrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\font\\local\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistVF.woff\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistVF.woff\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\font\\local\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistMonoVF.woff\",\"variable\":\"--font-geist-mono\",\"weight\":\"100 900\"}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistMonoVF.woff\",\"variable\":\"--font-geist-mono\",\"weight\":\"100 900\"}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Triumph\\ecommerce\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Triumph\\ecommerce\\provider\\AuthProvider.tsx":{"id":"(app-pages-browser)/./provider/AuthProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Triumph\\ecommerce\\provider\\SecureCartProvider.tsx":{"id":"(app-pages-browser)/./provider/SecureCartProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Triumph\\ecommerce\\styles\\product-card.css":{"id":"(app-pages-browser)/./styles/product-card.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Triumph\\ecommerce\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Triumph\\ecommerce\\app\\product\\[slug]\\page.tsx":{"id":"(app-pages-browser)/./app/product/[slug]/page.tsx","name":"*","chunks":[],"async":false},"D:\\Triumph\\ecommerce\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"D:\\Triumph\\ecommerce\\app\\cart\\page.tsx":{"id":"(app-pages-browser)/./app/cart/page.tsx","name":"*","chunks":[],"async":false},"D:\\Triumph\\ecommerce\\app\\account\\page.tsx":{"id":"(app-pages-browser)/./app/account/page.tsx","name":"*","chunks":[],"async":false},"D:\\Triumph\\ecommerce\\app\\test-recommendations\\page.tsx":{"id":"(app-pages-browser)/./app/test-recommendations/page.tsx","name":"*","chunks":[],"async":false},"D:\\Triumph\\ecommerce\\app\\shop\\page.tsx":{"id":"(app-pages-browser)/./app/shop/page.tsx","name":"*","chunks":["app/shop/page","static/chunks/app/shop/page.js"],"async":false}},"entryCSSFiles":{"D:\\Triumph\\ecommerce\\":[],"D:\\Triumph\\ecommerce\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Triumph\\ecommerce\\app\\page":[{"inlined":false,"path":"static/css/app/page.css"}],"D:\\Triumph\\ecommerce\\app\\shop\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./components/privacy/ConsentBanner.tsx":{"*":{"id":"(rsc)/./components/privacy/ConsentBanner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/utils/JsonLdWrapper.tsx":{"*":{"id":"(rsc)/./components/utils/JsonLdWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./provider/AuthProvider.tsx":{"*":{"id":"(rsc)/./provider/AuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./provider/SecureCartProvider.tsx":{"*":{"id":"(rsc)/./provider/SecureCartProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./styles/product-card.css":{"*":{"id":"(rsc)/./styles/product-card.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/product/[slug]/page.tsx":{"*":{"id":"(rsc)/./app/product/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/login/page.tsx":{"*":{"id":"(rsc)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cart/page.tsx":{"*":{"id":"(rsc)/./app/cart/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/account/page.tsx":{"*":{"id":"(rsc)/./app/account/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/test-recommendations/page.tsx":{"*":{"id":"(rsc)/./app/test-recommendations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/shop/page.tsx":{"*":{"id":"(rsc)/./app/shop/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}