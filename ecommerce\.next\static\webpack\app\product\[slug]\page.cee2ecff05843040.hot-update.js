"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/ProductRecommendations.tsx":
/*!*******************************************************!*\
  !*** ./components/product/ProductRecommendations.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductRecommendations: () => (/* binding */ ProductRecommendations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecommendations */ \"(app-pages-browser)/./hooks/useRecommendations.ts\");\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst ProductRecommendations = (param)=>{\n    let { type = 'all', limit = 8, title, showTitle = true, className = \"\", layout = 'grid' } = param;\n    _s();\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { getRecommendations, getRecommendationsByType } = (0,_hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations)();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    console.log(\"called recommandation>>>>>>>>>>>>>>>>>>\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductRecommendations.useEffect\": ()=>{\n            const fetchRecommendations = {\n                \"ProductRecommendations.useEffect.fetchRecommendations\": async ()=>{\n                    if (status === 'authenticated') {\n                        setLoading(true);\n                        try {\n                            const data = type === 'all' ? await getRecommendations(undefined, limit) : await getRecommendationsByType(type, limit);\n                            setRecommendations(data || []);\n                        } catch (error) {\n                            console.error('Error fetching recommendations:', error);\n                            setRecommendations([]);\n                        } finally{\n                            setLoading(false);\n                        }\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"ProductRecommendations.useEffect.fetchRecommendations\"];\n            fetchRecommendations();\n        }\n    }[\"ProductRecommendations.useEffect\"], [\n        status,\n        type,\n        limit,\n        getRecommendations,\n        getRecommendationsByType\n    ]);\n    if (status !== 'authenticated' || !loading && !recommendations.length) {\n        return null;\n    }\n    const getTitle = ()=>{\n        console.log(\"get title called>>>>>>>>>>>\");\n        if (title) return title;\n        console.log(\"title recommandation>>>\", title);\n        const titles = {\n            category_based: 'Recommended for You',\n            brand_based: 'From Your Favorite Brands',\n            price_based: 'In Your Price Range',\n            collaborative: 'Customers Also Liked',\n            trending: 'Trending Now',\n            frequently_bought: 'Frequently Bought Together',\n            all: 'Recommended Products'\n        };\n        return titles[type] || 'Recommended Products';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"product-recommendations \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: getTitle()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 23\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderGrid = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n            children: recommendations.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    ...product\n                }, product.id, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 90,\n            columnNumber: 5\n        }, undefined);\n    const renderCarousel = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 pb-4\",\n                children: recommendations.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            ...product\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined)\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 98,\n            columnNumber: 5\n        }, undefined);\n    const renderList = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: recommendations.map((product)=>{\n                var _product_images_, _product_images, _product_brand;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-4 p-4 border rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n                            alt: product.name,\n                            className: \"w-20 h-20 object-cover rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold\",\n                                    children: product.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: (_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        product.price\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, product.id, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 110,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"product-recommendations \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: getTitle()\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, undefined),\n            layout === 'grid' && renderGrid(),\n            layout === 'carousel' && renderCarousel(),\n            layout === 'list' && renderList()\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductRecommendations, \"i2oQ83dupDMMCX97ryAqc/4RPws=\", false, function() {\n    return [\n        _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = ProductRecommendations;\nvar _c;\n$RefreshReg$(_c, \"ProductRecommendations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/ProductRecommendations.tsx\n"));

/***/ })

});