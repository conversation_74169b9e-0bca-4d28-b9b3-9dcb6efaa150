"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./hooks/useRecommendations.ts":
/*!*************************************!*\
  !*** ./hooks/useRecommendations.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRecommendations: () => (/* binding */ useRecommendations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\n\n\n\nconst useRecommendations = ()=>{\n    const { data, loading, read } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL);\n    const { create } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL);\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const getRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getRecommendations]\": async (type, limit)=>{\n            if (status === 'authenticated') {\n                try {\n                    const params = new URLSearchParams();\n                    if (type && type !== 'all') params.append('type', type);\n                    if (limit) params.append('limit', limit.toString());\n                    const url = \"/api/v1/users/recommendations/\".concat(params.toString() ? '?' + params.toString() : '');\n                    const response = await read(url);\n                    const data = Array.isArray(response) ? response : [];\n                    setRecommendations(data);\n                    return data;\n                } catch (error) {\n                    console.error('Error fetching recommendations:', error);\n                    setRecommendations([]);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecommendations.useCallback[getRecommendations]\"], [\n        status,\n        read\n    ]);\n    const getRecommendationsByType = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getRecommendationsByType]\": async (type, limit)=>{\n            if (status === 'authenticated' && type !== 'all') {\n                try {\n                    const params = limit ? \"?limit=\".concat(limit) : '';\n                    const response = await read(\"/api/v1/users/recommendations/\".concat(type, \"/\").concat(params));\n                    return response || [];\n                } catch (error) {\n                    console.error('Error fetching recommendations by type:', error);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecommendations.useCallback[getRecommendationsByType]\"], [\n        status,\n        read\n    ]);\n    const generateRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[generateRecommendations]\": async ()=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/recommendations/generate/', {});\n                    return response;\n                } catch (error) {\n                    console.error('Error generating recommendations:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecommendations.useCallback[generateRecommendations]\"], [\n        status,\n        create\n    ]);\n    const getProductRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getProductRecommendations]\": async (productId, limit)=>{\n            try {\n                const params = limit ? \"?limit=\".concat(limit) : '';\n                const response = await read(\"/api/v1/users/recommendations/for-product/\".concat(productId, \"/\").concat(params));\n                return response || [];\n            } catch (error) {\n                console.error('Error fetching product recommendations:', error);\n                return [];\n            }\n        }\n    }[\"useRecommendations.useCallback[getProductRecommendations]\"], [\n        read\n    ]);\n    const trackInteraction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[trackInteraction]\": async (productId, interactionType, metadata)=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/interactions/track/', {\n                        product_id: productId,\n                        interaction_type: interactionType,\n                        metadata: metadata || {}\n                    });\n                    return response;\n                } catch (error) {\n                    console.error('Error tracking interaction:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecommendations.useCallback[trackInteraction]\"], [\n        status,\n        create\n    ]);\n    return {\n        recommendations,\n        loading,\n        getRecommendations,\n        getRecommendationsByType,\n        generateRecommendations,\n        getProductRecommendations,\n        trackInteraction\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useRecommendations.ts\n"));

/***/ })

});