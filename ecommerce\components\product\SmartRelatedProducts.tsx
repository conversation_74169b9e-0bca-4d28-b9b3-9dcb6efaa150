import React, { useState, useEffect } from 'react';
import { useRecommendations } from '../../hooks/useRecommendations';
import { MAIN_URL, CATEGORIZE_PRODUCTS } from '../../constant/urls';
import ProductCard from './ProductCard';
import { ProductType } from '../../types/product.d';
import axios from 'axios';

interface SmartRelatedProductsProps {
  productId: number;
  categorySlug?: string;
  limit?: number;
  className?: string;
}

export const SmartRelatedProducts: React.FC<SmartRelatedProductsProps> = ({
  productId,
  categorySlug,
  limit = 8,
  className = ""
}) => {
  const [relatedProducts, setRelatedProducts] = useState<ProductType[]>([]);
  const [loading, setLoading] = useState(true);
  const { getProductRecommendations } = useRecommendations();

  useEffect(() => {
    const fetchRelatedProducts = async () => {
      setLoading(true);
      try {
        // Try to get smart recommendations first
        const smartRecommendations = await getProductRecommendations(productId, limit);

        if (Array.isArray(smartRecommendations) && smartRecommendations.length > 0) {
          // Transform smart recommendations to ProductType
          const transformedRecommendations: ProductType[] = smartRecommendations.map((product: any) => ({
            id: product.id,
            name: product.name,
            price: product.price,
            rating: 0,
            image: product.images?.[0]?.image || '/placeholder.jpg',
            category: product.category?.name || '',
            brand: product.brand?.name || '',
            slug: product.slug
          }));
          setRelatedProducts(transformedRecommendations);
        } else if (categorySlug) {
          // Fallback to category-based products
          const endpoint = CATEGORIZE_PRODUCTS(categorySlug);
          const response = await axios.get(`${MAIN_URL}${endpoint}`);
          const categoryProducts = response.data?.results?.products || [];

          // Filter out current product and transform to ProductType
          const filtered = categoryProducts.filter((p: any) => p.id !== productId);
          const transformedProducts: ProductType[] = filtered.slice(0, limit).map((product: any) => ({
            id: product.id,
            name: product.name,
            price: product.price,
            rating: product.rating || 0,
            image: product.images?.[0]?.image || '/placeholder.jpg',
            category: product.category?.name || '',
            brand: product.brand?.name || '',
            slug: product.slug
          }));
          setRelatedProducts(transformedProducts);
        }
      } catch (error) {
        console.error('Error fetching related products:', error);
        setRelatedProducts([]);
      } finally {
        setLoading(false);
      }
    };

    if (productId) {
      fetchRelatedProducts();
    }
  }, [productId, categorySlug, limit, getProductRecommendations]);

  if (loading) {
    return (
      <div className={`mt-16 ${className}`}>
        <h2 className="text-2xl font-bold mb-6">Related Products</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-300 h-48 rounded-lg mb-2"></div>
              <div className="bg-gray-300 h-4 rounded mb-1"></div>
              <div className="bg-gray-300 h-4 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!relatedProducts.length) {
    return null;
  }

  return (
    <div className={`mt-16 ${className}`}>
      <h2 className="text-2xl font-bold mb-6">Related Products</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {relatedProducts.map((product) => (
          <ProductCard key={product.id} {...product} />
        ))}
      </div>
    </div>
  );
};
