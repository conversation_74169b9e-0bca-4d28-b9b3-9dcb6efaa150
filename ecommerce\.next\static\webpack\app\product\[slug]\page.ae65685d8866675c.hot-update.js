"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./hooks/useProductInteractions.ts":
/*!*****************************************!*\
  !*** ./hooks/useProductInteractions.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useProductInteractions: () => (/* binding */ useProductInteractions)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useRecommendations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useRecommendations */ \"(app-pages-browser)/./hooks/useRecommendations.ts\");\n/* harmony import */ var _useRecentlyViewed__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useRecentlyViewed */ \"(app-pages-browser)/./hooks/useRecentlyViewed.ts\");\n\n\n\nconst useProductInteractions = ()=>{\n    const { trackInteraction, generateRecommendations } = (0,_useRecommendations__WEBPACK_IMPORTED_MODULE_1__.useRecommendations)();\n    const { addToRecentlyViewed } = (0,_useRecentlyViewed__WEBPACK_IMPORTED_MODULE_2__.useRecentlyViewed)();\n    const trackProductView = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackProductView]\": async (productId)=>{\n            try {\n                await Promise.all([\n                    addToRecentlyViewed(productId),\n                    trackInteraction(productId, 'view', {\n                        source: 'product_detail'\n                    })\n                ]);\n                // Generate recommendations after every 3rd view (to avoid too frequent generation)\n                const viewCount = parseInt(localStorage.getItem('viewCount') || '0') + 1;\n                localStorage.setItem('viewCount', viewCount.toString());\n                if (viewCount % 3 === 0) {\n                    // Generate recommendations in background (don't await to avoid blocking UI)\n                    generateRecommendations().catch(console.error);\n                }\n            } catch (error) {\n                console.error('Error tracking product view:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackProductView]\"], [\n        addToRecentlyViewed,\n        trackInteraction,\n        generateRecommendations\n    ]);\n    const trackAddToCart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackAddToCart]\": async (productId, quantity)=>{\n            try {\n                await trackInteraction(productId, 'add_to_cart', {\n                    quantity\n                });\n            } catch (error) {\n                console.error('Error tracking add to cart:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackAddToCart]\"], [\n        trackInteraction\n    ]);\n    const trackPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackPurchase]\": async (productId, quantity, price)=>{\n            try {\n                await trackInteraction(productId, 'purchase', {\n                    quantity,\n                    price\n                });\n            } catch (error) {\n                console.error('Error tracking purchase:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackPurchase]\"], [\n        trackInteraction\n    ]);\n    const trackWishlistAdd = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackWishlistAdd]\": async (productId)=>{\n            try {\n                await trackInteraction(productId, 'wishlist_add');\n            } catch (error) {\n                console.error('Error tracking wishlist add:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackWishlistAdd]\"], [\n        trackInteraction\n    ]);\n    const trackWishlistRemove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackWishlistRemove]\": async (productId)=>{\n            try {\n                await trackInteraction(productId, 'wishlist_remove');\n            } catch (error) {\n                console.error('Error tracking wishlist remove:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackWishlistRemove]\"], [\n        trackInteraction\n    ]);\n    const trackSearchClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackSearchClick]\": async (productId, searchQuery)=>{\n            try {\n                await trackInteraction(productId, 'search', {\n                    search_query: searchQuery\n                });\n            } catch (error) {\n                console.error('Error tracking search click:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackSearchClick]\"], [\n        trackInteraction\n    ]);\n    return {\n        trackProductView,\n        trackAddToCart,\n        trackPurchase,\n        trackWishlistAdd,\n        trackWishlistRemove,\n        trackSearchClick\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useProductInteractions.ts\n"));

/***/ })

});