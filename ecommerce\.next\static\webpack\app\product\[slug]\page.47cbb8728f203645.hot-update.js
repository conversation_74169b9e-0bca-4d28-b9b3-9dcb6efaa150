"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/ProductRecommendations.tsx":
/*!*******************************************************!*\
  !*** ./components/product/ProductRecommendations.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductRecommendations: () => (/* binding */ ProductRecommendations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecommendations */ \"(app-pages-browser)/./hooks/useRecommendations.ts\");\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst ProductRecommendations = (param)=>{\n    let { type = 'all', limit = 8, title, showTitle = true, className = \"\", layout = 'grid' } = param;\n    _s();\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { getRecommendations, getRecommendationsByType } = (0,_hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations)();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    console.log(\"called recommandation>>>>>>>>>>>>>>>>>>\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductRecommendations.useEffect\": ()=>{\n            const fetchRecommendations = {\n                \"ProductRecommendations.useEffect.fetchRecommendations\": async ()=>{\n                    if (status === 'authenticated') {\n                        setLoading(true);\n                        try {\n                            const data = type === 'all' ? await getRecommendations(undefined, limit) : await getRecommendationsByType(type, limit);\n                            setRecommendations(data || []);\n                        } catch (error) {\n                            console.error('Error fetching recommendations:', error);\n                            setRecommendations([]);\n                        } finally{\n                            setLoading(false);\n                        }\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"ProductRecommendations.useEffect.fetchRecommendations\"];\n            fetchRecommendations();\n        }\n    }[\"ProductRecommendations.useEffect\"], [\n        status,\n        type,\n        limit,\n        getRecommendations,\n        getRecommendationsByType\n    ]);\n    if (status !== 'authenticated' || !loading && !recommendations.length) {\n        return null;\n    }\n    const getTitle = ()=>{\n        if (title) return title;\n        const titles = {\n            category_based: 'Recommended for You',\n            brand_based: 'From Your Favorite Brands',\n            price_based: 'In Your Price Range',\n            collaborative: 'Customers Also Liked',\n            trending: 'Trending Now',\n            frequently_bought: 'Frequently Bought Together',\n            all: 'Recommended Products'\n        };\n        return titles[type] || 'Recommended Products';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"product-recommendations \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: getTitle()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 23\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, undefined);\n    }\n    const renderGrid = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n            children: recommendations.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    ...product\n                }, product.id, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 89,\n            columnNumber: 5\n        }, undefined);\n    const renderCarousel = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4 pb-4\",\n                children: recommendations.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            ...product\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, undefined)\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 97,\n            columnNumber: 5\n        }, undefined);\n    const renderList = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: recommendations.map((product)=>{\n                var _product_images_, _product_images, _product_brand;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-4 p-4 border rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n                            alt: product.name,\n                            className: \"w-20 h-20 object-cover rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold\",\n                                    children: product.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: (_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        product.price\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, product.id, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n            lineNumber: 109,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"product-recommendations \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: getTitle()\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, undefined),\n            layout === 'grid' && renderGrid(),\n            layout === 'carousel' && renderCarousel(),\n            layout === 'list' && renderList()\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductRecommendations.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductRecommendations, \"i2oQ83dupDMMCX97ryAqc/4RPws=\", false, function() {\n    return [\n        _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = ProductRecommendations;\nvar _c;\n$RefreshReg$(_c, \"ProductRecommendations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/ProductRecommendations.tsx\n"));

/***/ })

});