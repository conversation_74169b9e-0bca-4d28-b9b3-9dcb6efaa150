"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/useRecentlyViewed.ts":
/*!************************************!*\
  !*** ./hooks/useRecentlyViewed.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRecentlyViewed: () => (/* binding */ useRecentlyViewed)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\n\n\n\nconst useRecentlyViewed = ()=>{\n    const { data, loading, read } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL);\n    const { create } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL);\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const getRecentlyViewed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecentlyViewed.useCallback[getRecentlyViewed]\": async ()=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await read('/api/v1/users/recently-viewed/');\n                    const data = Array.isArray(response) ? response : [];\n                    setRecentlyViewed(data);\n                    return data;\n                } catch (error) {\n                    console.error('Error fetching recently viewed:', error);\n                    setRecentlyViewed([]);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecentlyViewed.useCallback[getRecentlyViewed]\"], [\n        status,\n        read\n    ]);\n    const addToRecentlyViewed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecentlyViewed.useCallback[addToRecentlyViewed]\": async (productId)=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/recently-viewed/add/', {\n                        product_id: productId\n                    });\n                    // Refresh the recently viewed list\n                    await getRecentlyViewed();\n                    return response;\n                } catch (error) {\n                    console.error('Error adding to recently viewed:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecentlyViewed.useCallback[addToRecentlyViewed]\"], [\n        status,\n        create,\n        getRecentlyViewed\n    ]);\n    const clearRecentlyViewed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecentlyViewed.useCallback[clearRecentlyViewed]\": async ()=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/recently-viewed/clear/', {});\n                    setRecentlyViewed([]);\n                    return response;\n                } catch (error) {\n                    console.error('Error clearing recently viewed:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecentlyViewed.useCallback[clearRecentlyViewed]\"], [\n        status,\n        create\n    ]);\n    // Auto-fetch recently viewed when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRecentlyViewed.useEffect\": ()=>{\n            if (status === 'authenticated') {\n                getRecentlyViewed();\n            } else {\n                setRecentlyViewed([]);\n            }\n        }\n    }[\"useRecentlyViewed.useEffect\"], [\n        status,\n        getRecentlyViewed\n    ]);\n    return {\n        recentlyViewed,\n        loading,\n        getRecentlyViewed,\n        addToRecentlyViewed,\n        clearRecentlyViewed\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useRecentlyViewed.ts\n"));

/***/ })

});