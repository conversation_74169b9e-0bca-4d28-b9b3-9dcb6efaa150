"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./hooks/useRecommendations.ts":
/*!*************************************!*\
  !*** ./hooks/useRecommendations.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRecommendations: () => (/* binding */ useRecommendations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\n\n\n\nconst useRecommendations = ()=>{\n    const { data, loading, read } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL);\n    const { create } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL);\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const getRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getRecommendations]\": async function(type, limit) {\n            let autoGenerate = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n            if (status === 'authenticated') {\n                try {\n                    const params = new URLSearchParams();\n                    if (type && type !== 'all') params.append('type', type);\n                    if (limit) params.append('limit', limit.toString());\n                    const url = \"/api/v1/users/recommendations/\".concat(params.toString() ? '?' + params.toString() : '');\n                    const response = await read(url);\n                    // If no recommendations found and autoGenerate is true, generate them\n                    if ((!response || Array.isArray(response) && response.length === 0) && autoGenerate) {\n                        console.log('No recommendations found, generating new ones...');\n                        await generateRecommendations();\n                        // Fetch again after generation\n                        const newResponse = await read(url);\n                        const newRecommendations = Array.isArray(newResponse) ? newResponse : [];\n                        setRecommendations(newRecommendations);\n                        return newRecommendations;\n                    }\n                    const recommendations = Array.isArray(response) ? response : [];\n                    setRecommendations(recommendations);\n                    return response;\n                } catch (error) {\n                    console.error('Error fetching recommendations:', error);\n                    setRecommendations([]);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecommendations.useCallback[getRecommendations]\"], [\n        status,\n        read\n    ]);\n    const getRecommendationsByType = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getRecommendationsByType]\": async function(type, limit) {\n            let autoGenerate = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n            if (status === 'authenticated' && type !== 'all') {\n                try {\n                    const params = limit ? \"?limit=\".concat(limit) : '';\n                    const response = await read(\"/api/v1/users/recommendations/\".concat(type, \"/\").concat(params));\n                    // If no recommendations found and autoGenerate is true, generate them\n                    if ((!response || Array.isArray(response) && response.length === 0) && autoGenerate) {\n                        console.log(\"No \".concat(type, \" recommendations found, generating new ones...\"));\n                        await generateRecommendations();\n                        // Fetch again after generation\n                        const newResponse = await read(\"/api/v1/users/recommendations/\".concat(type, \"/\").concat(params));\n                        return Array.isArray(newResponse) ? newResponse : [];\n                    }\n                    return Array.isArray(response) ? response : [];\n                } catch (error) {\n                    console.error('Error fetching recommendations by type:', error);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecommendations.useCallback[getRecommendationsByType]\"], [\n        status,\n        read\n    ]);\n    const generateRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[generateRecommendations]\": async ()=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/recommendations/generate/', {});\n                    return response;\n                } catch (error) {\n                    console.error('Error generating recommendations:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecommendations.useCallback[generateRecommendations]\"], [\n        status,\n        create\n    ]);\n    const getProductRecommendations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[getProductRecommendations]\": async (productId, limit)=>{\n            try {\n                const params = limit ? \"?limit=\".concat(limit) : '';\n                const response = await read(\"/api/v1/users/recommendations/for-product/\".concat(productId, \"/\").concat(params));\n                return response || [];\n            } catch (error) {\n                console.error('Error fetching product recommendations:', error);\n                return [];\n            }\n        }\n    }[\"useRecommendations.useCallback[getProductRecommendations]\"], [\n        read\n    ]);\n    const trackInteraction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecommendations.useCallback[trackInteraction]\": async (productId, interactionType, metadata)=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/interactions/track/', {\n                        product_id: productId,\n                        interaction_type: interactionType,\n                        metadata: metadata || {}\n                    });\n                    return response;\n                } catch (error) {\n                    console.error('Error tracking interaction:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecommendations.useCallback[trackInteraction]\"], [\n        status,\n        create\n    ]);\n    return {\n        recommendations,\n        loading,\n        getRecommendations,\n        getRecommendationsByType,\n        generateRecommendations,\n        getProductRecommendations,\n        trackInteraction\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useRecommendations.ts\n"));

/***/ })

});