import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRecommendations } from '../../hooks/useRecommendations';
import { Button } from '../ui/button';
import { RefreshCw, Zap } from 'lucide-react';

interface RecommendationRefresherProps {
  onRecommendationsUpdated?: () => void;
  showButton?: boolean;
  autoRefresh?: boolean;
  className?: string;
}

export const RecommendationRefresher: React.FC<RecommendationRefresherProps> = ({
  onRecommendationsUpdated,
  showButton = true,
  autoRefresh = false,
  className = ""
}) => {
  const { generateRecommendations } = useRecommendations();
  const { status } = useSession();
  const [isGenerating, setIsGenerating] = useState(false);
  const [lastGenerated, setLastGenerated] = useState<Date | null>(null);

  const handleGenerateRecommendations = async () => {
    if (status !== 'authenticated' || isGenerating) return;

    setIsGenerating(true);
    try {
      await generateRecommendations();
      setLastGenerated(new Date());
      onRecommendationsUpdated?.();
    } catch (error) {
      console.error('Error generating recommendations:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Auto-refresh recommendations when component mounts (if enabled)
  useEffect(() => {
    if (autoRefresh && status === 'authenticated' && !lastGenerated) {
      handleGenerateRecommendations();
    }
  }, [autoRefresh, status]);

  if (status !== 'authenticated') {
    return null;
  }

  return (
    <div className={`recommendation-refresher ${className}`}>
      {showButton && (
        <Button
          onClick={handleGenerateRecommendations}
          disabled={isGenerating}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          {isGenerating ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Zap className="h-4 w-4" />
          )}
          {isGenerating ? 'Generating...' : 'Refresh Recommendations'}
        </Button>
      )}
      
      {lastGenerated && (
        <p className="text-xs text-gray-500 mt-2">
          Last updated: {lastGenerated.toLocaleTimeString()}
        </p>
      )}
    </div>
  );
};
