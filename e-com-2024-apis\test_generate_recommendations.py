#!/usr/bin/env python
"""
Test script to verify the generate recommendations endpoint
"""

import os
import sys
import django
import requests
import json

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token

Customer = get_user_model()

def test_generate_recommendations_endpoint():
    """Test the generate recommendations endpoint"""
    print("Testing generate recommendations endpoint...")
    
    # Get or create a test user
    user, created = Customer.objects.get_or_create(
        email='<EMAIL>',
        defaults={'name': 'Test User'}
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"Created test user: {user.email}")
    else:
        print(f"Using existing test user: {user.email}")
    
    # Create or get token for authentication
    token, created = Token.objects.get_or_create(user=user)
    print(f"Using token: {token.key[:10]}...")
    
    # Test the endpoint
    base_url = "http://127.0.0.1:8000/api/v1"
    headers = {
        'Authorization': f'Bearer {token.key}',
        'Content-Type': 'application/json'
    }
    
    try:
        # Test POST to generate recommendations
        response = requests.post(
            f"{base_url}/users/recommendations/generate/",
            headers=headers,
            json={}
        )
        
        print(f"POST /users/recommendations/generate/ - Status: {response.status_code}")
        
        if response.status_code == 200 or response.status_code == 201:
            data = response.json()
            print(f"Success! Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"Exception occurred: {e}")
        return False

def test_get_recommendations():
    """Test getting recommendations"""
    print("\nTesting get recommendations endpoint...")
    
    user = Customer.objects.get(email='<EMAIL>')
    token = Token.objects.get(user=user)
    
    base_url = "http://127.0.0.1:8000/api/v1"
    headers = {
        'Authorization': f'Bearer {token.key}',
        'Content-Type': 'application/json'
    }
    
    try:
        # Test GET recommendations
        response = requests.get(
            f"{base_url}/users/recommendations/",
            headers=headers
        )
        
        print(f"GET /users/recommendations/ - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success! Found {len(data)} recommendations")
            return True
        else:
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"Exception occurred: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Testing Recommendation Generation Endpoint ===\n")
    
    try:
        # Test generate endpoint
        generate_success = test_generate_recommendations_endpoint()
        
        # Test get endpoint
        get_success = test_get_recommendations()
        
        if generate_success and get_success:
            print("\n✅ All tests passed! Recommendation generation is working.")
        else:
            print("\n❌ Some tests failed. Check the output above.")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
