"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx":
/*!*******************************************************!*\
  !*** ./components/product/RecentlyViewedProducts.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentlyViewedProducts: () => (/* binding */ RecentlyViewedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useRecentlyViewed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecentlyViewed */ \"(app-pages-browser)/./hooks/useRecentlyViewed.ts\");\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst RecentlyViewedProducts = (param)=>{\n    let { limit = 5, showTitle = true, className = \"\" } = param;\n    _s();\n    const { recentlyViewed, loading, getRecentlyViewed } = (0,_hooks_useRecentlyViewed__WEBPACK_IMPORTED_MODULE_3__.useRecentlyViewed)();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentlyViewedProducts.useEffect\": ()=>{\n            if (status === 'authenticated') {\n                getRecentlyViewed();\n            }\n        }\n    }[\"RecentlyViewedProducts.useEffect\"], [\n        status,\n        getRecentlyViewed\n    ]);\n    if (status !== 'authenticated' || !loading && !(recentlyViewed === null || recentlyViewed === void 0 ? void 0 : recentlyViewed.length)) {\n        return null;\n    }\n    const displayProducts = recentlyViewed.slice(0, limit);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"recently-viewed-products \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Recently Viewed\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"recently-viewed-products \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Recently Viewed\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                children: displayProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        ...product\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecentlyViewedProducts, \"+FbsAKR3eVFfgCS5lvwQ/Pns/q0=\", false, function() {\n    return [\n        _hooks_useRecentlyViewed__WEBPACK_IMPORTED_MODULE_3__.useRecentlyViewed,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = RecentlyViewedProducts;\nvar _c;\n$RefreshReg$(_c, \"RecentlyViewedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\n"));

/***/ })

});