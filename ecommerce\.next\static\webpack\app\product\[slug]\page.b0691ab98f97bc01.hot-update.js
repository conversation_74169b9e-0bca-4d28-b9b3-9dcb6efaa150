"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./hooks/useRecentlyViewed.ts":
/*!************************************!*\
  !*** ./hooks/useRecentlyViewed.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRecentlyViewed: () => (/* binding */ useRecentlyViewed)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\n\n\n\nconst useRecentlyViewed = ()=>{\n    const { loading, read } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    const { create } = (0,_useApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const getRecentlyViewed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecentlyViewed.useCallback[getRecentlyViewed]\": async ()=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await read('/api/v1/users/recently-viewed/');\n                    const data = Array.isArray(response) ? response : [];\n                    setRecentlyViewed(data);\n                    return data;\n                } catch (error) {\n                    console.error('Error fetching recently viewed:', error);\n                    setRecentlyViewed([]);\n                    return [];\n                }\n            }\n            return [];\n        }\n    }[\"useRecentlyViewed.useCallback[getRecentlyViewed]\"], [\n        status,\n        read\n    ]);\n    const addToRecentlyViewed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecentlyViewed.useCallback[addToRecentlyViewed]\": async (productId)=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/recently-viewed/add/', {\n                        product_id: productId\n                    });\n                    // Refresh the recently viewed list\n                    await getRecentlyViewed();\n                    return response;\n                } catch (error) {\n                    console.error('Error adding to recently viewed:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecentlyViewed.useCallback[addToRecentlyViewed]\"], [\n        status,\n        create,\n        getRecentlyViewed\n    ]);\n    const clearRecentlyViewed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRecentlyViewed.useCallback[clearRecentlyViewed]\": async ()=>{\n            if (status === 'authenticated') {\n                try {\n                    const response = await create('/api/v1/users/recently-viewed/clear/', {});\n                    setRecentlyViewed([]);\n                    return response;\n                } catch (error) {\n                    console.error('Error clearing recently viewed:', error);\n                    return null;\n                }\n            }\n        }\n    }[\"useRecentlyViewed.useCallback[clearRecentlyViewed]\"], [\n        status,\n        create\n    ]);\n    // Auto-fetch recently viewed when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRecentlyViewed.useEffect\": ()=>{\n            if (status === 'authenticated') {\n                getRecentlyViewed();\n            } else {\n                setRecentlyViewed([]);\n            }\n        }\n    }[\"useRecentlyViewed.useEffect\"], [\n        status,\n        getRecentlyViewed\n    ]);\n    return {\n        recentlyViewed,\n        loading,\n        getRecentlyViewed,\n        addToRecentlyViewed,\n        clearRecentlyViewed\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useRecentlyViewed.ts\n"));

/***/ })

});