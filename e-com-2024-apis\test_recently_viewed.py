#!/usr/bin/env python
"""
Simple test script to verify the recently viewed products and recommendations functionality
"""

import os
import sys
import django
import requests
import json

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from products.models import Product
from users.models import RecentlyViewedProduct, UserProductInteraction
from users.recommendation_engine import RecommendationEngine

Customer = get_user_model()

def test_models():
    """Test that our new models work correctly"""
    print("Testing models...")
    
    # Get or create a test user
    user, created = Customer.objects.get_or_create(
        email='<EMAIL>',
        defaults={'name': 'Test User'}
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"Created test user: {user.email}")
    else:
        print(f"Using existing test user: {user.email}")
    
    # Get a test product
    product = Product.objects.filter(is_active=True).first()
    if not product:
        print("No active products found. Please add some products first.")
        return False
    
    print(f"Using test product: {product.name}")
    
    # Test RecentlyViewedProduct
    recently_viewed, created = RecentlyViewedProduct.objects.get_or_create(
        user=user,
        product=product
    )
    print(f"Recently viewed product {'created' if created else 'updated'}: {recently_viewed}")
    
    # Test UserProductInteraction
    interaction = UserProductInteraction.objects.create(
        user=user,
        product=product,
        interaction_type='view',
        metadata={'source': 'test'}
    )
    print(f"Created interaction: {interaction}")
    
    # Test RecommendationEngine
    engine = RecommendationEngine(user)
    recommendations = engine.generate_all_recommendations(limit_per_type=3)
    print(f"Generated recommendations: {len(recommendations)} types")
    for rec_type, products in recommendations.items():
        print(f"  {rec_type}: {len(products)} products")
    
    return True

def test_api_endpoints():
    """Test the API endpoints (requires authentication)"""
    print("\nTesting API endpoints...")
    
    base_url = "http://127.0.0.1:8000/api/v1"
    
    # Test endpoints that don't require authentication
    endpoints_to_test = [
        "/users/recommendations/for-product/1/",
        "/products/",
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            print(f"GET {endpoint}: Status {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    print(f"  Returned {len(data)} items")
                elif isinstance(data, dict):
                    print(f"  Returned data with keys: {list(data.keys())}")
        except Exception as e:
            print(f"Error testing {endpoint}: {e}")
    
    print("\nNote: Authentication-required endpoints need to be tested with a logged-in user.")
    return True

def main():
    """Run all tests"""
    print("=== Testing Recently Viewed Products Implementation ===\n")
    
    try:
        # Test models
        if not test_models():
            return
        
        # Test API endpoints
        test_api_endpoints()
        
        print("\n=== All tests completed successfully! ===")
        print("\nThe recently viewed products and recommendations system is working correctly.")
        print("You can now:")
        print("1. View products to add them to recently viewed")
        print("2. Add products to cart to track interactions")
        print("3. Generate personalized recommendations")
        print("4. Use the new React components in the frontend")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
