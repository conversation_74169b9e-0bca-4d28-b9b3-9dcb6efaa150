#!/usr/bin/env python
"""
Simple test to verify recommendation generation works
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from products.models import Product
from users.models import UserProductInteraction
from users.recommendation_engine import RecommendationEngine

Customer = get_user_model()

def test_recommendation_generation():
    """Test recommendation generation directly"""
    print("Testing recommendation generation...")
    
    # Get or create a test user
    user, created = Customer.objects.get_or_create(
        email='<EMAIL>',
        defaults={'name': 'Test User'}
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"Created test user: {user.email}")
    else:
        print(f"Using existing test user: {user.email}")
    
    # Get some products to create interactions
    products = Product.objects.filter(is_active=True)[:5]
    if not products:
        print("No active products found. Please add some products first.")
        return False
    
    print(f"Found {len(products)} products to work with")
    
    # Create some test interactions
    for i, product in enumerate(products):
        interaction_type = ['view', 'add_to_cart', 'purchase'][i % 3]
        UserProductInteraction.objects.get_or_create(
            user=user,
            product=product,
            interaction_type=interaction_type,
            defaults={'interaction_weight': 1.0}
        )
        print(f"Created {interaction_type} interaction for {product.name}")
    
    # Test the recommendation engine
    engine = RecommendationEngine(user)
    recommendations = engine.generate_all_recommendations(limit_per_type=3)
    
    print(f"\nGenerated recommendations:")
    total_recommendations = 0
    for rec_type, products in recommendations.items():
        print(f"  {rec_type}: {len(products)} products")
        total_recommendations += len(products)
    
    print(f"\nTotal recommendations generated: {total_recommendations}")
    
    if total_recommendations > 0:
        print("✅ Recommendation generation is working!")
        return True
    else:
        print("❌ No recommendations were generated")
        return False

def test_views():
    """Test the Django views"""
    print("\nTesting Django views...")
    
    from django.test import Client
    from django.contrib.auth import authenticate
    
    client = Client()
    
    # Get test user
    user = Customer.objects.get(email='<EMAIL>')
    
    # Login the user
    client.force_login(user)
    
    # Test the generate recommendations view
    response = client.post('/api/v1/users/recommendations/generate/', {}, content_type='application/json')
    print(f"POST /api/v1/users/recommendations/generate/ - Status: {response.status_code}")
    
    if response.status_code in [200, 201]:
        print("✅ Generate recommendations endpoint is working!")
        
        # Test getting recommendations
        response = client.get('/api/v1/users/recommendations/')
        print(f"GET /api/v1/users/recommendations/ - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {len(data)} recommendations in API response")
            return True
        else:
            print(f"❌ Error getting recommendations: {response.content}")
            return False
    else:
        print(f"❌ Error generating recommendations: {response.content}")
        return False

def main():
    """Run all tests"""
    print("=== Testing Recommendation System ===\n")
    
    try:
        # Test recommendation generation
        generation_success = test_recommendation_generation()
        
        # Test Django views
        views_success = test_views()
        
        if generation_success and views_success:
            print("\n🎉 All tests passed! The recommendation system is working correctly.")
            print("\nYou can now:")
            print("1. Visit the frontend and add products to cart")
            print("2. The recommendations will be generated automatically")
            print("3. Check the test page at http://localhost:3000/test-recommendations")
        else:
            print("\n❌ Some tests failed. Check the output above.")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
