import { useCallback } from 'react';
import { useRecommendations } from './useRecommendations';
import { useRecentlyViewed } from './useRecentlyViewed';

export const useProductInteractions = () => {
  const { trackInteraction, generateRecommendations } = useRecommendations();
  const { addToRecentlyViewed } = useRecentlyViewed();

  const trackProductView = useCallback(async (productId: number) => {
    try {
      await Promise.all([
        addToRecentlyViewed(productId),
        trackInteraction(productId, 'view', { source: 'product_detail' })
      ]);

      // Generate recommendations after every 3rd view (to avoid too frequent generation)
      const viewCount = parseInt(localStorage.getItem('viewCount') || '0') + 1;
      localStorage.setItem('viewCount', viewCount.toString());

      if (viewCount % 3 === 0) {
        // Generate recommendations in background (don't await to avoid blocking UI)
        generateRecommendations().catch(console.error);
      }
    } catch (error) {
      console.error('Error tracking product view:', error);
    }
  }, [addToRecentlyViewed, trackInteraction, generateRecommendations]);

  const trackAddToCart = useCallback(async (productId: number, quantity: number) => {
    try {
      await trackInteraction(productId, 'add_to_cart', { quantity });

      // Generate recommendations after cart addition (high-value interaction)
      generateRecommendations().catch(console.error);
    } catch (error) {
      console.error('Error tracking add to cart:', error);
    }
  }, [trackInteraction, generateRecommendations]);

  const trackPurchase = useCallback(async (productId: number, quantity: number, price: number) => {
    try {
      await trackInteraction(productId, 'purchase', { quantity, price });

      // Always generate recommendations after purchase (highest-value interaction)
      generateRecommendations().catch(console.error);
    } catch (error) {
      console.error('Error tracking purchase:', error);
    }
  }, [trackInteraction, generateRecommendations]);

  const trackWishlistAdd = useCallback(async (productId: number) => {
    try {
      await trackInteraction(productId, 'wishlist_add');

      // Generate recommendations after wishlist addition
      generateRecommendations().catch(console.error);
    } catch (error) {
      console.error('Error tracking wishlist add:', error);
    }
  }, [trackInteraction, generateRecommendations]);

  const trackWishlistRemove = useCallback(async (productId: number) => {
    try {
      await trackInteraction(productId, 'wishlist_remove');

      // Generate recommendations after wishlist removal (to update preferences)
      generateRecommendations().catch(console.error);
    } catch (error) {
      console.error('Error tracking wishlist remove:', error);
    }
  }, [trackInteraction, generateRecommendations]);

  const trackSearchClick = useCallback(async (productId: number, searchQuery: string) => {
    try {
      await trackInteraction(productId, 'search', { search_query: searchQuery });
    } catch (error) {
      console.error('Error tracking search click:', error);
    }
  }, [trackInteraction]);

  return {
    trackProductView,
    trackAddToCart,
    trackPurchase,
    trackWishlistAdd,
    trackWishlistRemove,
    trackSearchClick
  };
};
