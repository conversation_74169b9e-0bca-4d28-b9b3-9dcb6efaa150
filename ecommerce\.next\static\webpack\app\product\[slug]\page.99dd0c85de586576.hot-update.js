"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./hooks/useProductInteractions.ts":
/*!*****************************************!*\
  !*** ./hooks/useProductInteractions.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useProductInteractions: () => (/* binding */ useProductInteractions)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useRecommendations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useRecommendations */ \"(app-pages-browser)/./hooks/useRecommendations.ts\");\n/* harmony import */ var _useRecentlyViewed__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useRecentlyViewed */ \"(app-pages-browser)/./hooks/useRecentlyViewed.ts\");\n\n\n\nconst useProductInteractions = ()=>{\n    const { trackInteraction, generateRecommendations } = (0,_useRecommendations__WEBPACK_IMPORTED_MODULE_1__.useRecommendations)();\n    const { addToRecentlyViewed } = (0,_useRecentlyViewed__WEBPACK_IMPORTED_MODULE_2__.useRecentlyViewed)();\n    const trackProductView = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackProductView]\": async (productId)=>{\n            try {\n                await Promise.all([\n                    addToRecentlyViewed(productId),\n                    trackInteraction(productId, 'view', {\n                        source: 'product_detail'\n                    })\n                ]);\n                // Generate recommendations after every 3rd view (to avoid too frequent generation)\n                const viewCount = parseInt(localStorage.getItem('viewCount') || '0') + 1;\n                localStorage.setItem('viewCount', viewCount.toString());\n                if (viewCount % 3 === 0) {\n                    // Generate recommendations in background (don't await to avoid blocking UI)\n                    generateRecommendations().catch(console.error);\n                }\n            } catch (error) {\n                console.error('Error tracking product view:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackProductView]\"], [\n        addToRecentlyViewed,\n        trackInteraction,\n        generateRecommendations\n    ]);\n    const trackAddToCart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackAddToCart]\": async (productId, quantity)=>{\n            try {\n                await trackInteraction(productId, 'add_to_cart', {\n                    quantity\n                });\n                // Generate recommendations after cart addition (high-value interaction)\n                generateRecommendations().catch(console.error);\n            } catch (error) {\n                console.error('Error tracking add to cart:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackAddToCart]\"], [\n        trackInteraction,\n        generateRecommendations\n    ]);\n    const trackPurchase = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackPurchase]\": async (productId, quantity, price)=>{\n            try {\n                await trackInteraction(productId, 'purchase', {\n                    quantity,\n                    price\n                });\n                // Always generate recommendations after purchase (highest-value interaction)\n                generateRecommendations().catch(console.error);\n            } catch (error) {\n                console.error('Error tracking purchase:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackPurchase]\"], [\n        trackInteraction,\n        generateRecommendations\n    ]);\n    const trackWishlistAdd = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackWishlistAdd]\": async (productId)=>{\n            try {\n                await trackInteraction(productId, 'wishlist_add');\n                // Generate recommendations after wishlist addition\n                generateRecommendations().catch(console.error);\n            } catch (error) {\n                console.error('Error tracking wishlist add:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackWishlistAdd]\"], [\n        trackInteraction,\n        generateRecommendations\n    ]);\n    const trackWishlistRemove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackWishlistRemove]\": async (productId)=>{\n            try {\n                await trackInteraction(productId, 'wishlist_remove');\n                // Generate recommendations after wishlist removal (to update preferences)\n                generateRecommendations().catch(console.error);\n            } catch (error) {\n                console.error('Error tracking wishlist remove:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackWishlistRemove]\"], [\n        trackInteraction,\n        generateRecommendations\n    ]);\n    const trackSearchClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useProductInteractions.useCallback[trackSearchClick]\": async (productId, searchQuery)=>{\n            try {\n                await trackInteraction(productId, 'search', {\n                    search_query: searchQuery\n                });\n            } catch (error) {\n                console.error('Error tracking search click:', error);\n            }\n        }\n    }[\"useProductInteractions.useCallback[trackSearchClick]\"], [\n        trackInteraction\n    ]);\n    return {\n        trackProductView,\n        trackAddToCart,\n        trackPurchase,\n        trackWishlistAdd,\n        trackWishlistRemove,\n        trackSearchClick\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useProductInteractions.ts\n"));

/***/ })

});