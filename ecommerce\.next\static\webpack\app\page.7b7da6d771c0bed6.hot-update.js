"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx":
/*!*******************************************************!*\
  !*** ./components/product/RecentlyViewedProducts.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentlyViewedProducts: () => (/* binding */ RecentlyViewedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useRecentlyViewed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecentlyViewed */ \"(app-pages-browser)/./hooks/useRecentlyViewed.ts\");\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst RecentlyViewedProducts = (param)=>{\n    let { limit = 5, showTitle = true, className = \"\" } = param;\n    _s();\n    const { recentlyViewed, loading, getRecentlyViewed } = (0,_hooks_useRecentlyViewed__WEBPACK_IMPORTED_MODULE_3__.useRecentlyViewed)();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentlyViewedProducts.useEffect\": ()=>{\n            if (status === 'authenticated') {\n                getRecentlyViewed();\n            }\n        }\n    }[\"RecentlyViewedProducts.useEffect\"], [\n        status,\n        getRecentlyViewed\n    ]);\n    if (status !== 'authenticated' || !loading && !(recentlyViewed === null || recentlyViewed === void 0 ? void 0 : recentlyViewed.length)) {\n        return null;\n    }\n    const displayProducts = recentlyViewed.slice(0, limit);\n    // Transform products to match ProductType interface\n    const transformedProducts = displayProducts.map((product)=>{\n        var _product_images_, _product_images, _product_category, _product_brand;\n        return {\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            rating: 0,\n            image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n            category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name) || '',\n            brand: ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name) || '',\n            slug: product.slug\n        };\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"recently-viewed-products \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Recently Viewed\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"recently-viewed-products \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Recently Viewed\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                children: transformedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        ...product\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecentlyViewedProducts, \"+FbsAKR3eVFfgCS5lvwQ/Pns/q0=\", false, function() {\n    return [\n        _hooks_useRecentlyViewed__WEBPACK_IMPORTED_MODULE_3__.useRecentlyViewed,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = RecentlyViewedProducts;\nvar _c;\n$RefreshReg$(_c, \"RecentlyViewedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\n"));

/***/ })

});