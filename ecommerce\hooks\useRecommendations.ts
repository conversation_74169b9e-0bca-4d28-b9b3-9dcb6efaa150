import { useCallback, useState } from 'react';
import { useSession } from 'next-auth/react';
import useApi from './useApi';
import { MAIN_URL } from '../constant/urls';

export interface RecommendedProduct {
  id: number;
  name: string;
  slug: string;
  price: number;
  images: Array<{ image: string }>;
  brand?: { name: string };
  category?: { name: string };
}

export type RecommendationType = 
  | 'category_based' 
  | 'brand_based' 
  | 'price_based' 
  | 'collaborative' 
  | 'frequently_bought' 
  | 'trending' 
  | 'all';

export const useRecommendations = () => {
  const { loading, read } = useApi(MAIN_URL || '');
  const { create } = useApi(MAIN_URL || '');
  const { status } = useSession();
  const [recommendations, setRecommendations] = useState<RecommendedProduct[]>([]);

  const getRecommendations = useCallback(async (type?: RecommendationType, limit?: number) => {
    if (status === 'authenticated') {
      try {
        const params = new URLSearchParams();
        if (type && type !== 'all') params.append('type', type);
        if (limit) params.append('limit', limit.toString());

        const url = `/api/v1/users/recommendations/${params.toString() ? '?' + params.toString() : ''}`;
        const response = await read(url);
        const data = Array.isArray(response) ? response : [];
        setRecommendations(data);
        return data;
      } catch (error) {
        console.error('Error fetching recommendations:', error);
        setRecommendations([]);
        return [];
      }
    }
    return [];
  }, [status, read]);

  const getRecommendationsByType = useCallback(async (type: RecommendationType, limit?: number) => {
    if (status === 'authenticated' && type !== 'all') {
      try {
        const params = limit ? `?limit=${limit}` : '';
        const response = await read(`/api/v1/users/recommendations/${type}/${params}`);
        return Array.isArray(response) ? response : [];
      } catch (error) {
        console.error('Error fetching recommendations by type:', error);
        return [];
      }
    }
    return [];
  }, [status, read]);

  const generateRecommendations = useCallback(async () => {
    if (status === 'authenticated') {
      try {
        const response = await create('/api/v1/users/recommendations/generate/', {});
        return response;
      } catch (error) {
        console.error('Error generating recommendations:', error);
        return null;
      }
    }
  }, [status, create]);

  const getProductRecommendations = useCallback(async (productId: number, limit?: number) => {
    try {
      const params = limit ? `?limit=${limit}` : '';
      const response = await read(`/api/v1/users/recommendations/for-product/${productId}/${params}`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching product recommendations:', error);
      return [];
    }
  }, [read]);

  const trackInteraction = useCallback(async (
    productId: number,
    interactionType: string,
    metadata?: any
  ) => {
    if (status === 'authenticated') {
      try {
        const response = await create('/api/v1/users/interactions/track/', {
          product_id: productId,
          interaction_type: interactionType,
          metadata: metadata || {}
        });
        return response;
      } catch (error) {
        console.error('Error tracking interaction:', error);
        return null;
      }
    }
  }, [status, create]);

  return {
    recommendations,
    loading,
    getRecommendations,
    getRecommendationsByType,
    generateRecommendations,
    getProductRecommendations,
    trackInteraction
  };
};
