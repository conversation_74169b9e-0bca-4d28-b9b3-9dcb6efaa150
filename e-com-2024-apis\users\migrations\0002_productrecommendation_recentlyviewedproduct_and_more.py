# Generated by Django 5.0.2 on 2025-07-30 13:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0004_alter_product_height_alter_product_length_and_more'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recommendation_type', models.CharField(choices=[('category_based', 'Category Based'), ('brand_based', 'Brand Based'), ('price_based', 'Price Based'), ('collaborative', 'Collaborative Filtering'), ('frequently_bought', 'Frequently Bought Together'), ('trending', 'Trending Products'), ('seasonal', 'Seasonal Recommendations')], max_length=20)),
                ('score', models.FloatField(help_text='Recommendation confidence score (0.0 to 1.0)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product')),
                ('source_product', models.ForeignKey(blank=True, help_text='Product that triggered this recommendation', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='generated_recommendations', to='products.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-score', '-created_at'],
                'indexes': [models.Index(fields=['user', 'recommendation_type', '-score'], name='users_produ_user_id_acd13a_idx'), models.Index(fields=['product', 'recommendation_type'], name='users_produ_product_ef6703_idx'), models.Index(fields=['source_product', 'recommendation_type'], name='users_produ_source__f07faa_idx'), models.Index(fields=['created_at'], name='users_produ_created_6c3bac_idx')],
                'unique_together': {('user', 'product', 'recommendation_type', 'source_product')},
            },
        ),
        migrations.CreateModel(
            name='RecentlyViewedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('viewed_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recently_viewed', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-viewed_at'],
                'indexes': [models.Index(fields=['user', '-viewed_at'], name='users_recen_user_id_c86663_idx'), models.Index(fields=['product', '-viewed_at'], name='users_recen_product_b2e986_idx')],
                'unique_together': {('user', 'product')},
            },
        ),
        migrations.CreateModel(
            name='UserProductInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interaction_type', models.CharField(choices=[('view', 'Product View'), ('add_to_cart', 'Add to Cart'), ('remove_from_cart', 'Remove from Cart'), ('purchase', 'Purchase'), ('wishlist_add', 'Add to Wishlist'), ('wishlist_remove', 'Remove from Wishlist'), ('review', 'Product Review'), ('search', 'Search Result Click')], max_length=20)),
                ('interaction_weight', models.FloatField(default=1.0, help_text='Weight of this interaction for recommendations')),
                ('session_id', models.CharField(blank=True, help_text='Session ID for anonymous user tracking', max_length=100, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Additional interaction data (search query, cart quantity, etc.)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_interactions', to='products.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_interactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'interaction_type', '-created_at'], name='users_userp_user_id_821a15_idx'), models.Index(fields=['product', 'interaction_type', '-created_at'], name='users_userp_product_50bda9_idx'), models.Index(fields=['session_id', '-created_at'], name='users_userp_session_b6db8d_idx'), models.Index(fields=['created_at'], name='users_userp_created_745856_idx')],
            },
        ),
    ]
