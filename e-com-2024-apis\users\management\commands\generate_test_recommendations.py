from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from products.models import Product
from users.models import UserProductInteraction
from users.recommendation_engine import RecommendationEngine

Customer = get_user_model()

class Command(BaseCommand):
    help = 'Generate test recommendations for a user'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='Email of the user to generate recommendations for'
        )

    def handle(self, *args, **options):
        email = options['email']
        
        try:
            # Get or create test user
            user, created = Customer.objects.get_or_create(
                email=email,
                defaults={'name': 'Test User'}
            )
            
            if created:
                user.set_password('testpass123')
                user.save()
                self.stdout.write(f"Created test user: {email}")
            else:
                self.stdout.write(f"Using existing user: {email}")
            
            # Create some test interactions if none exist
            if not UserProductInteraction.objects.filter(user=user).exists():
                products = Product.objects.filter(is_active=True)[:10]
                if products:
                    for i, product in enumerate(products):
                        interaction_type = ['view', 'add_to_cart', 'purchase'][i % 3]
                        UserProductInteraction.objects.create(
                            user=user,
                            product=product,
                            interaction_type=interaction_type,
                            interaction_weight=1.0
                        )
                    self.stdout.write(f"Created {len(products)} test interactions")
                else:
                    self.stdout.write("No products found to create interactions")
            
            # Generate recommendations
            engine = RecommendationEngine(user)
            recommendations = engine.generate_all_recommendations()
            
            total_count = sum(len(products) for products in recommendations.values())
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully generated {total_count} recommendations for {email}'
                )
            )
            
            for rec_type, products in recommendations.items():
                self.stdout.write(f"  {rec_type}: {len(products)} products")
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error generating recommendations: {e}')
            )
