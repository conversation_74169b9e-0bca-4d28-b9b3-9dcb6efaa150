"use client";
import { useEffect, useState } from "react";
import { ProductInfo } from "../../../components/product/ProductInfo";
import { SmartRelatedProducts } from "../../../components/product/SmartRelatedProducts";
import { ProductRecommendations } from "../../../components/product/ProductRecommendations";
import useApi from "../../../hooks/useApi";
import { useProductInteractions } from "../../../hooks/useProductInteractions";
import { MAIN_URL, PRODUCTS } from "../../../constant/urls";
import { useParams } from "next/navigation";
import MainHOF from "../../../layout/MainHOF";
import SingleProductLoading from "@/components/ui/loading/SingleProductLoading";
import ImageCarousel from "@/components/product/ImageCarousel";
import { ProductMetadata } from "./ProductMetadata";
import { PincodeValidator } from "@/components/shipping/PincodeValidator";
import { useSession } from "next-auth/react";

const ProductDetail = () => {
  const { slug } = useParams();
  const [selectedColor, setSelectedColor] = useState("");
  const [selectedSize, setSelectedSize] = useState("");
  const [quantity, setQuantity] = useState(1);
  const { data, loading, read } = useApi(MAIN_URL || "");
  const { status } = useSession();
  const { trackProductView } = useProductInteractions();

  // State for pincode
  const [pincode, setPincode] = useState("");
  const [pincodeValid, setPincodeValid] = useState(false);
  const [pincodeServiceable, setPincodeServiceable] = useState(false);

  useEffect(() => {
    console.log("Fetching product details for slug:", slug);
    read(PRODUCTS + slug);
  }, [slug]);

  // Log the product data when it changes
  useEffect(() => {
    if (data) {
      console.log("Product data loaded:", data);
    }
  }, [data]);

  // Mock product data - in a real app, this would come from an API
  const product: any = data ?? undefined;

  // Track product view when product is loaded
  useEffect(() => {
    if (product?.id && status === 'authenticated') {
      trackProductView(product.id);
    }
  }, [product?.id, status, trackProductView]);

  if (loading) {
    return (
      <MainHOF>
        <div className="container mx-auto px-4 py-8">
          <SingleProductLoading />
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      {product && <ProductMetadata product={product} />}
      <div className="container mx-auto px-4 py-4">
        {/* Mobile-first responsive layout */}
        <div className="flex flex-col lg:grid lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 lg:items-start">
          {product && (
            <>
              {/* Image carousel - responsive container */}
              <div className="w-full order-1 lg:order-1">
                <div className="w-full p-3 sm:p-4 border rounded-md border-slate-200 flex items-center justify-center lg:sticky lg:top-4">
                  {Array.isArray(product.images) && (
                    <ImageCarousel
                      productImage={product.images}
                      brand={product.brand}
                    />
                  )}
                </div>
              </div>

              {/* Product info - responsive container */}
              <div className="w-full order-2 lg:order-2 mt-4 lg:mt-0">
                <ProductInfo
                  product={product}
                  selectedColor={selectedColor}
                  selectedSize={selectedSize}
                  quantity={quantity}
                  onColorChange={setSelectedColor}
                  onSizeChange={setSelectedSize}
                  onQuantityChange={setQuantity}
                />
                {/* Delivery Pincode Validator */}
              {status === "authenticated" &&   <div className="mt-6">
                  <PincodeValidator
                    value={pincode}
                    onChange={setPincode}
                    onValidationChange={(isValid, isServiceable) => {
                      setPincodeValid(isValid);
                      setPincodeServiceable(isServiceable);
                    }}
                  />
                  {/* Optionally, show a message below */}
                  {pincode && pincode.length === 6 && (
                    <div className="mt-2 text-sm">
                      {pincodeValid && pincodeServiceable && (
                        <span className="text-green-600 font-medium">
                          Delivery available to this pincode!
                        </span>
                      )}
                      {/* {pincodeValid && !pincodeServiceable && (
                        <span className="text-orange-600 font-medium">
                          Sorry, delivery is not available to this pincode.
                        </span>
                      )} */}
                      {/* {!pincodeValid && (
                        <span className="text-red-600 font-medium">
                          Invalid pincode.
                        </span>
                      )} */}
                    </div>
                  )}
                </div>}
              </div>
            </>
          )}
        </div>

        {/* Smart Related products with proper spacing */}
        <div className="mt-8 lg:mt-12">
          <SmartRelatedProducts
            productId={product?.id || 0}
            categorySlug={product?.category?.slug || ""}
            limit={8}
          />
        </div>

        {/* Frequently bought together */}
        {status === 'authenticated' && (
          <div className="mt-8">
            <ProductRecommendations
              type="frequently_bought"
              limit={4}
              title="Frequently Bought Together"
              layout="grid"
            />
          </div>
        )}
      </div>
    </MainHOF>
  );
};

export default ProductDetail;
